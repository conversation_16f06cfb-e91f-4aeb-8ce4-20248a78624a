package nutrition

import (
	"context"

	"shikeyinxiang-goframe/api/nutrition/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) DailyNutrition(ctx context.Context, req *v1.DailyNutritionReq) (res *v1.DailyNutritionRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.DailyNutritionInput{
		UserId: req.UserId,
		Date:   req.Date,
	}

	// 2. 调用业务逻辑
	output, err := service.Nutrition().GetUserDailyNutrition(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.DailyNutritionRes{
		NutritionData: output.NutritionData,
		Date:          output.Date,
	}

	return res, nil
}
