package user

import (
	"context"

	"shikeyinxiang-goframe/api/user/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) AvatarDownloadUrl(ctx context.Context, req *v1.AvatarDownloadUrlReq) (res *v1.AvatarDownloadUrlRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.AvatarDownloadUrlInput{
		UserId:  req.UserId,
		FileKey: req.FileKey,
	}

	// 2. 调用业务逻辑
	output, err := service.User().GetAvatarDownloadUrl(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.AvatarDownloadUrlRes{
		DownloadUrl: output.DownloadUrl,
	}

	return res, nil
}
