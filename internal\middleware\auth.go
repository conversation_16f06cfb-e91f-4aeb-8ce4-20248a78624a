package middleware

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/golang-jwt/jwt/v5"

	"shikeyinxiang-goframe/internal/consts"
)

// Auth 认证中间件
func Auth(r *ghttp.Request) {
	// 1. 获取Authorization header
	authHeader := r.Header.Get("Authorization")
	if authHeader == "" {
		r.Response.WriteJsonExit(g.Map{
			"code":    401,
			"message": "未提供认证token",
			"data":    nil,
		})
		return
	}

	// 2. 提取Bearer token
	tokenString := ""
	if strings.HasPrefix(authHeader, "Bearer ") {
		tokenString = authHeader[7:]
	} else {
		r.Response.WriteJsonExit(g.Map{
			"code":    401,
			"message": "认证token格式错误",
			"data":    nil,
		})
		return
	}

	// 3. 验证token
	userInfo, err := validateToken(r.Context(), tokenString)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code":    401,
			"message": "无法认证",
			"data":    nil,
		})
		return
	}

	// 4. 将用户信息存储到上下文
	ctx := context.WithValue(r.Context(), consts.CtxUserInfo, userInfo)
	r.SetCtx(ctx)

	// 继续执行
	r.Middleware.Next()
}

// UserInfo 用户信息结构
type UserInfo struct {
	UserId   int64  `json:"userId"`
	Username string `json:"username"`
	Role     string `json:"role"`
	JTI      string `json:"jti"`
}

// validateToken 验证JWT token（从logic中移植的健全实现）
func validateToken(ctx context.Context, tokenString string) (*UserInfo, error) {
	// 定义keyFunc用于验证签名
	keyFunc := func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, gerror.Newf("未预期的签名算法: %v", token.Header["alg"])
		}
		jwtSecret := g.Cfg().MustGet(ctx, "jwt.secret").String()
		return []byte(jwtSecret), nil
	}

	// 解析token并验证
	claims := jwt.MapClaims{}
	token, err := jwt.ParseWithClaims(tokenString, claims, keyFunc)
	if err != nil {
		return nil, gerror.New("Token解析或校验失败: " + err.Error())
	}

	// 确保token是有效的
	if !token.Valid {
		return nil, gerror.New("无效的Token")
	}

	// 检查JTI是否存在
	jti, jtiExists := claims["jti"]
	if !jtiExists || jti == nil {
		return nil, gerror.New("token中缺少jti声明")
	}

	// 检查黑名单（使用规范的Redis键名）
	jtiStr := gconv.String(jti)
	blacklistKey := fmt.Sprintf("jwt:blacklist:jti:%s", jtiStr)
	existsResult, err := g.Redis().Do(ctx, "EXISTS", blacklistKey)
	if err != nil {
		return nil, gerror.New("检查token黑名单失败: " + err.Error())
	}
	if existsResult.Int() > 0 {
		return nil, gerror.New("token已被注销")
	}

	// 检查过期时间
	exp, expExists := claims["exp"]
	if !expExists || exp == nil {
		return nil, gerror.New("token中缺少exp声明")
	}

	// 验证是否过期
	expTime := time.Unix(gconv.Int64(exp), 0)
	if time.Now().After(expTime) {
		return nil, gerror.New("token已过期")
	}

	// 构造用户信息
	userInfo := &UserInfo{
		UserId:   gconv.Int64(claims["userId"]),
		Username: gconv.String(claims["username"]),
		Role:     gconv.String(claims["role"]),
		JTI:      jtiStr,
	}

	return userInfo, nil
}


