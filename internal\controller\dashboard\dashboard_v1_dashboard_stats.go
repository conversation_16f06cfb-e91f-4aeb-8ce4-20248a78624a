package dashboard

import (
	"context"

	"shikeyinxiang-goframe/api/dashboard/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) DashboardStats(ctx context.Context, req *v1.DashboardStatsReq) (res *v1.DashboardStatsRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.DashboardStatsInput{
		DateRange: req.DateRange,
	}

	// 2. 调用业务逻辑
	output, err := service.Dashboard().GetDashboardStats(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.DashboardStatsRes{
		TotalUsers:         output.TotalUsers,
		TodayRecords:       output.TodayRecords,
		NutritionCompliance: output.NutritionCompliance,
		ActiveUsers:        output.ActiveUsers,
		TotalRecords:       output.TotalRecords,
		AvgCaloriesPerDay:  output.AvgCaloriesPerDay,
	}

	return res, nil
}
