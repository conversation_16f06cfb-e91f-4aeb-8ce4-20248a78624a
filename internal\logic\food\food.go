package food

import (
	"context"
	"fmt"
	"strings"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"

	"shikeyinxiang-goframe/internal/dao"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/model/do"
	"shikeyinxiang-goframe/internal/model/entity"
	"shikeyinxiang-goframe/internal/service"
)

type sFood struct{}

func init() {
	service.RegisterFood(New())
}

func New() service.IFood {
	return &sFood{}
}

// GetFoodList 分页查询食物列表
func (s *sFood) GetFoodList(ctx context.Context, in *model.FoodQueryInput) (*model.FoodListOutput, error) {
	// 构建查询条件
	query := dao.Food.Ctx(ctx)

	// 分类筛选
	if in.CategoryId != nil {
		query = query.Where(dao.Food.Columns().CategoryId, *in.CategoryId)
	}

	// 关键词搜索
	if in.Keyword != "" {
		keyword := "%" + in.Keyword + "%"
		query = query.Where(fmt.Sprintf("(%s LIKE ? OR %s LIKE ?)", 
			dao.Food.Columns().FoodName, dao.Food.Columns().Measure), keyword, keyword)
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		return nil, err
	}

	// 分页查询
	offset := (in.Page - 1) * in.Size
	var foods []*entity.Food
	err = query.Limit(in.Size).Offset(offset).OrderDesc(dao.Food.Columns().Id).Scan(&foods)
	if err != nil {
		return nil, err
	}

	// 转换为FoodInfo
	foodInfos := make([]*model.FoodInfo, 0, len(foods))
	for _, food := range foods {
		foodInfo, err := s.convertToFoodInfo(ctx, food)
		if err != nil {
			return nil, err
		}
		foodInfos = append(foodInfos, foodInfo)
	}

	return &model.FoodListOutput{
		List:  foodInfos,
		Total: int64(total),
		Page:  in.Page,
		Size:  in.Size,
	}, nil
}

// GetFoodByID 根据ID获取食物详情
func (s *sFood) GetFoodByID(ctx context.Context, id int) (*model.FoodInfo, error) {
	var food *entity.Food
	err := dao.Food.Ctx(ctx).Where(dao.Food.Columns().Id, id).Scan(&food)
	if err != nil {
		return nil, err
	}
	if food == nil {
		return nil, gerror.NewCode(CodeFoodNotFound, "食物不存在")
	}

	return s.convertToFoodInfo(ctx, food)
}

// CreateFood 创建食物
func (s *sFood) CreateFood(ctx context.Context, in *model.FoodCreateInput) (*model.FoodCreateOutput, error) {
	// 1. 数据验证
	if err := s.validateFoodCreateInput(ctx, in); err != nil {
		return nil, err
	}

	// 2. 检查食物名称是否已存在
	if err := s.checkFoodNameExists(ctx, in.Name, 0); err != nil {
		return nil, err
	}

	// 3. 验证分类是否存在
	if in.CategoryId > 0 {
		category, err := service.FoodCategory().GetCategoryByID(ctx, in.CategoryId)
		if err != nil {
			return nil, err
		}
		if category == nil {
			return nil, gerror.NewCode(CodeFoodCategoryNotFound, "食物分类不存在")
		}
	}

	// 4. 创建食物记录
	foodDO := &do.Food{
		FoodName:   in.Name,
		Measure:    in.Measure,
		Grams:      in.Grams,
		Calories:   in.Calories,
		Protein:    in.Protein,
		Fat:        in.Fat,
		SatFat:     in.SatFat,
		Fiber:      in.Fiber,
		Carbs:      in.Carbs,
		CategoryId: in.CategoryId,
		ImageUrl:   in.ImageUrl,
	}

	result, err := dao.Food.Ctx(ctx).Data(foodDO).Insert()
	if err != nil {
		// 处理数据库约束错误
		if strings.Contains(err.Error(), "food_name") {
			return nil, gerror.NewCode(CodeFoodAlreadyExists, "食物名称已存在")
		}
		return nil, err
	}

	// 5. 获取创建的食物ID
	foodId, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}

	// 6. 返回创建的食物信息
	foodInfo, err := s.GetFoodByID(ctx, int(foodId))
	if err != nil {
		return nil, err
	}

	return &model.FoodCreateOutput{
		FoodInfo: foodInfo,
	}, nil
}

// UpdateFood 更新食物信息
func (s *sFood) UpdateFood(ctx context.Context, in *model.FoodUpdateInput) (*model.FoodUpdateOutput, error) {
	// 1. 验证食物是否存在
	existFood, err := s.GetFoodByID(ctx, in.Id)
	if err != nil {
		return nil, err
	}
	if existFood == nil {
		return nil, gerror.NewCode(CodeFoodNotFound, "食物不存在")
	}

	// 2. 检查食物名称是否已被其他食物使用
	if in.Name != "" {
		if err := s.checkFoodNameExists(ctx, in.Name, in.Id); err != nil {
			return nil, err
		}
	}

	// 3. 验证分类是否存在
	if in.CategoryId > 0 {
		category, err := service.FoodCategory().GetCategoryByID(ctx, in.CategoryId)
		if err != nil {
			return nil, err
		}
		if category == nil {
			return nil, gerror.NewCode(CodeFoodCategoryNotFound, "食物分类不存在")
		}
	}

	// 4. 构建更新数据
	updateData := &do.Food{}
	
	if in.Name != "" {
		updateData.FoodName = in.Name
	}
	if in.Measure != "" {
		updateData.Measure = in.Measure
	}
	if in.Grams != 0 {
		updateData.Grams = in.Grams
	}
	if in.Calories != 0 {
		updateData.Calories = in.Calories
	}
	if in.Protein != 0 {
		updateData.Protein = in.Protein
	}
	if in.Fat != 0 {
		updateData.Fat = in.Fat
	}
	if in.SatFat != 0 {
		updateData.SatFat = in.SatFat
	}
	if in.Fiber != "" {
		updateData.Fiber = in.Fiber
	}
	if in.Carbs != 0 {
		updateData.Carbs = in.Carbs
	}
	if in.CategoryId != 0 {
		updateData.CategoryId = in.CategoryId
	}
	if in.ImageUrl != "" {
		updateData.ImageUrl = in.ImageUrl
	}

	// 5. 执行更新
	_, err = dao.Food.Ctx(ctx).Where(dao.Food.Columns().Id, in.Id).Data(updateData).Update()
	if err != nil {
		return &model.FoodUpdateOutput{
			Success: false,
			Message: "更新失败: " + err.Error(),
		}, nil
	}

	return &model.FoodUpdateOutput{
		Success: true,
		Message: "更新成功",
	}, nil
}

// DeleteFood 删除食物
func (s *sFood) DeleteFood(ctx context.Context, id int) error {
	// 验证食物是否存在
	existFood, err := s.GetFoodByID(ctx, id)
	if err != nil {
		return err
	}
	if existFood == nil {
		return gerror.NewCode(CodeFoodNotFound, "食物不存在")
	}

	// 删除食物
	_, err = dao.Food.Ctx(ctx).Where(dao.Food.Columns().Id, id).Delete()
	return err
}

// UpdateFoodImage 更新食物图片
func (s *sFood) UpdateFoodImage(ctx context.Context, in *model.FoodImageUpdateInput) (*model.FoodImageUpdateOutput, error) {
	// 验证食物是否存在
	existFood, err := s.GetFoodByID(ctx, in.Id)
	if err != nil {
		return nil, err
	}
	if existFood == nil {
		return nil, gerror.NewCode(CodeFoodNotFound, "食物不存在")
	}

	// 更新图片URL
	_, err = dao.Food.Ctx(ctx).Where(dao.Food.Columns().Id, in.Id).Data(do.Food{
		ImageUrl: in.ImageUrl,
	}).Update()

	if err != nil {
		return &model.FoodImageUpdateOutput{
			Success: false,
			Message: "图片更新失败: " + err.Error(),
		}, nil
	}

	return &model.FoodImageUpdateOutput{
		Success: true,
		Message: "图片更新成功",
	}, nil
}

// BatchImportFoods 批量导入食物
func (s *sFood) BatchImportFoods(ctx context.Context, in *model.FoodBatchImportInput) (*model.FoodBatchImportOutput, error) {
	if len(in.Foods) == 0 {
		return nil, gerror.NewCode(CodeFoodInvalidFormat, "导入食物列表不能为空")
	}

	successCount := 0
	failCount := 0
	var errors []string

	// 使用事务处理批量导入
	err := dao.Food.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		for i, foodInput := range in.Foods {
			// 验证每个食物数据
			if err := s.validateFoodCreateInput(ctx, foodInput); err != nil {
				errors = append(errors, fmt.Sprintf("第%d个食物: %s", i+1, err.Error()))
				failCount++
				continue
			}

			// 创建食物
			_, err := s.CreateFood(ctx, foodInput)
			if err != nil {
				errors = append(errors, fmt.Sprintf("第%d个食物: %s", i+1, err.Error()))
				failCount++
				continue
			}

			successCount++
		}
		return nil
	})

	if err != nil {
		return &model.FoodBatchImportOutput{
			Success:      false,
			Message:      "批量导入失败: " + err.Error(),
			SuccessCount: 0,
			FailCount:    len(in.Foods),
		}, nil
	}

	message := fmt.Sprintf("批量导入完成，成功: %d，失败: %d", successCount, failCount)
	if len(errors) > 0 {
		message += "，错误详情: " + strings.Join(errors, "; ")
	}

	return &model.FoodBatchImportOutput{
		Success:      successCount > 0,
		Message:      message,
		SuccessCount: successCount,
		FailCount:    failCount,
	}, nil
}

// GetAllCategories 获取所有分类名称列表
func (s *sFood) GetAllCategories(ctx context.Context) ([]string, error) {
	categories, err := service.FoodCategory().GetAllCategories(ctx)
	if err != nil {
		return nil, err
	}

	categoryNames := make([]string, 0, len(categories))
	for _, category := range categories {
		categoryNames = append(categoryNames, category.Name)
	}

	return categoryNames, nil
}

// convertToFoodInfo 将entity.Food转换为model.FoodInfo
func (s *sFood) convertToFoodInfo(ctx context.Context, food *entity.Food) (*model.FoodInfo, error) {
	if food == nil {
		return nil, nil
	}

	foodInfo := &model.FoodInfo{
		Id:         food.Id,
		Name:       food.FoodName,
		Measure:    food.Measure,
		Grams:      food.Grams,
		Calories:   food.Calories,
		Protein:    food.Protein,
		Fat:        food.Fat,
		SatFat:     food.SatFat,
		Fiber:      food.Fiber,
		Carbs:      food.Carbs,
		CategoryId: food.CategoryId,
		ImageUrl:   food.ImageUrl,
	}

	// 获取分类信息
	if food.CategoryId > 0 {
		category, err := service.FoodCategory().GetCategoryByID(ctx, food.CategoryId)
		if err != nil {
			// 如果获取分类失败，记录日志但不影响主流程
			g.Log().Warningf(ctx, "获取食物分类失败: %v", err)
		} else if category != nil {
			foodInfo.Category = category.Name
			foodInfo.CategoryInfo = category
		}
	}

	return foodInfo, nil
}

// validateFoodCreateInput 验证食物创建输入参数
func (s *sFood) validateFoodCreateInput(ctx context.Context, in *model.FoodCreateInput) error {
	if in.Name == "" {
		return gerror.NewCode(CodeFoodNameRequired, "食物名称不能为空")
	}

	// 验证营养成分数据
	if in.Calories < 0 || in.Protein < 0 || in.Fat < 0 || in.SatFat < 0 || in.Carbs < 0 || in.Grams < 0 {
		return gerror.NewCode(CodeFoodInvalidNutrition, "营养成分数据不能为负数")
	}

	return nil
}

// checkFoodNameExists 检查食物名称是否已被其他食物使用
func (s *sFood) checkFoodNameExists(ctx context.Context, name string, excludeFoodId int) error {
	query := dao.Food.Ctx(ctx).Where(dao.Food.Columns().FoodName, name)
	if excludeFoodId > 0 {
		query = query.WhereNot(dao.Food.Columns().Id, excludeFoodId)
	}

	count, err := query.Count()
	if err != nil {
		return err
	}
	if count > 0 {
		return gerror.NewCode(CodeFoodAlreadyExists, "食物名称已存在")
	}
	return nil
}
