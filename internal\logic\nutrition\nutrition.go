package nutrition

import (
	"context"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

type sNutrition struct{}

func init() {
	service.RegisterNutrition(New())
}

func New() service.INutrition {
	return &sNutrition{}
}

// GetDailyNutritionStat 获取用户日营养统计
func (s *sNutrition) GetDailyNutritionStat(ctx context.Context, in *model.NutritionDailyStatInput) (*model.NutritionDailyStatOutput, error) {
	if in.UserId <= 0 {
		return nil, gerror.NewCode(CodeNutritionInvalidUser, "用户ID无效")
	}
	if in.Date == "" {
		return nil, gerror.NewCode(CodeNutritionInvalidDate, "日期不能为空")
	}

	// 验证日期格式
	_, err := time.Parse("2006-01-02", in.Date)
	if err != nil {
		return nil, gerror.NewCode(CodeNutritionInvalidDate, "日期格式无效")
	}

	// 获取用户营养目标
	userGoal, err := service.User().GetUserNutritionGoal(ctx, &model.UserNutritionGoalQueryInput{
		UserId: in.UserId,
	})
	if err != nil {
		return nil, gerror.NewCode(CodeNutritionGoalQueryFailed, "获取营养目标失败")
	}

	// 获取当日饮食记录
	dietRecords, err := service.Diet().GetDietRecords(ctx, &model.DietRecordQueryInput{
		UserId:    in.UserId,
		StartDate: in.Date,
		EndDate:   in.Date,
		Page:      1,
		Size:      1000, // 获取当日所有记录
	})
	if err != nil {
		return nil, gerror.NewCode(CodeNutritionCalculationFailed, "计算营养统计失败")
	}

	// 计算营养摄入总量
	var totalCalorie float64
	var totalProtein float64
	var totalCarbs float64
	var totalFat float64

	for _, record := range dietRecords.List {
		for _, food := range record.Foods {
			totalCalorie += food.Calories
			totalProtein += food.Protein
			totalCarbs += food.Carbs
			totalFat += food.Fat
		}
	}

	// 计算目标达成百分比
	var caloriePercentage, proteinPercentage, carbsPercentage, fatPercentage float64

	if userGoal.Goal.CalorieGoal > 0 {
		caloriePercentage = (totalCalorie / float64(userGoal.Goal.CalorieGoal)) * 100
	}
	if userGoal.Goal.ProteinGoal > 0 {
		proteinPercentage = (totalProtein / userGoal.Goal.ProteinGoal) * 100
	}
	if userGoal.Goal.CarbsGoal > 0 {
		carbsPercentage = (totalCarbs / userGoal.Goal.CarbsGoal) * 100
	}
	if userGoal.Goal.FatGoal > 0 {
		fatPercentage = (totalFat / userGoal.Goal.FatGoal) * 100
	}

	stat := model.NutritionStat{
		Date:              in.Date,
		Calorie:           int(totalCalorie),
		Protein:           totalProtein,
		Carbs:             totalCarbs,
		Fat:               totalFat,
		CaloriePercentage: caloriePercentage,
		ProteinPercentage: proteinPercentage,
		CarbsPercentage:   carbsPercentage,
		FatPercentage:     fatPercentage,
	}

	return &model.NutritionDailyStatOutput{
		Stat: stat,
	}, nil
}

// GetNutritionTrend 获取用户营养趋势
func (s *sNutrition) GetNutritionTrend(ctx context.Context, in *model.NutritionTrendInput) (*model.NutritionTrendOutput, error) {
	if in.UserId <= 0 {
		return nil, gerror.NewCode(CodeNutritionInvalidUser, "用户ID无效")
	}
	if in.StartDate == "" || in.EndDate == "" {
		return nil, gerror.NewCode(CodeNutritionInvalidDate, "开始日期和结束日期不能为空")
	}

	// 验证日期格式
	startDate, err := time.Parse("2006-01-02", in.StartDate)
	if err != nil {
		return nil, gerror.NewCode(CodeNutritionInvalidDate, "开始日期格式无效")
	}
	endDate, err := time.Parse("2006-01-02", in.EndDate)
	if err != nil {
		return nil, gerror.NewCode(CodeNutritionInvalidDate, "结束日期格式无效")
	}

	if startDate.After(endDate) {
		return nil, gerror.NewCode(CodeNutritionInvalidDate, "开始日期不能晚于结束日期")
	}

	// 批量获取饮食记录
	batchRecords, err := service.Diet().GetBatchDietRecordsForNutritionStat(ctx, &model.DietRecordBatchQueryInput{
		UserIds:   []int64{in.UserId},
		StartDate: in.StartDate,
		EndDate:   in.EndDate,
	})
	if err != nil {
		return nil, gerror.NewCode(CodeNutritionCalculationFailed, "计算营养趋势失败")
	}

	// 生成日期列表
	var dateList []string
	var calorieList []int
	var proteinList []float64
	var carbsList []float64
	var fatList []float64

	current := startDate
	for !current.After(endDate) {
		dateStr := current.Format("2006-01-02")
		dateList = append(dateList, dateStr)

		// 计算当日营养摄入
		var dayCalorie float64
		var dayProtein float64
		var dayCarbs float64
		var dayFat float64

		if userRecords, exists := batchRecords.Records[in.UserId]; exists {
			if dayRecords, exists := userRecords[dateStr]; exists {
				for _, record := range dayRecords {
					for _, food := range record.Foods {
						dayCalorie += food.Calories
						dayProtein += food.Protein
						dayCarbs += food.Carbs
						dayFat += food.Fat
					}
				}
			}
		}

		calorieList = append(calorieList, int(dayCalorie))
		proteinList = append(proteinList, dayProtein)
		carbsList = append(carbsList, dayCarbs)
		fatList = append(fatList, dayFat)

		current = current.AddDate(0, 0, 1)
	}

	trend := model.NutritionTrend{
		DateList:    dateList,
		CalorieList: calorieList,
		ProteinList: proteinList,
		CarbsList:   carbsList,
		FatList:     fatList,
	}

	return &model.NutritionTrendOutput{
		Trend: trend,
	}, nil
}

// GetNutritionDetails 获取用户营养摄入详情
func (s *sNutrition) GetNutritionDetails(ctx context.Context, in *model.NutritionDetailsInput) (*model.NutritionDetailsOutput, error) {
	// 获取当日营养统计
	statResult, err := s.GetDailyNutritionStat(ctx, &model.NutritionDailyStatInput{
		UserId: in.UserId,
		Date:   in.Date,
	})
	if err != nil {
		return nil, err
	}

	stat := statResult.Stat

	// 构建营养详情列表
	details := []model.NutritionDetailItem{
		{
			Name:       "热量",
			Value:      float64(stat.Calorie),
			Unit:       "千卡",
			Percentage: stat.CaloriePercentage,
		},
		{
			Name:       "蛋白质",
			Value:      stat.Protein,
			Unit:       "克",
			Percentage: stat.ProteinPercentage,
		},
		{
			Name:       "碳水化合物",
			Value:      stat.Carbs,
			Unit:       "克",
			Percentage: stat.CarbsPercentage,
		},
		{
			Name:       "脂肪",
			Value:      stat.Fat,
			Unit:       "克",
			Percentage: stat.FatPercentage,
		},
	}

	return &model.NutritionDetailsOutput{
		Details: details,
	}, nil
}

// GetNutritionAdvice 获取用户营养建议
func (s *sNutrition) GetNutritionAdvice(ctx context.Context, in *model.NutritionAdviceInput) (*model.NutritionAdviceOutput, error) {
	// 获取当日营养统计
	statResult, err := s.GetDailyNutritionStat(ctx, &model.NutritionDailyStatInput{
		UserId: in.UserId,
		Date:   in.Date,
	})
	if err != nil {
		return nil, err
	}

	stat := statResult.Stat
	var advices []model.NutritionAdviceDisplay

	// 根据营养摄入百分比生成建议
	advices = append(advices, s.generateAdviceByPercentage("protein", stat.ProteinPercentage)...)
	advices = append(advices, s.generateAdviceByPercentage("carbs", stat.CarbsPercentage)...)
	advices = append(advices, s.generateAdviceByPercentage("fat", stat.FatPercentage)...)
	advices = append(advices, s.generateAdviceByPercentage("calorie", stat.CaloriePercentage)...)

	return &model.NutritionAdviceOutput{
		Advices: advices,
	}, nil
}

// CalculateNutritionComplianceRate 计算指定日期的营养达标率
func (s *sNutrition) CalculateNutritionComplianceRate(ctx context.Context, in *model.NutritionComplianceRateInput) (*model.NutritionComplianceRateOutput, error) {
	if in.Date == "" {
		return nil, gerror.NewCode(CodeNutritionInvalidDate, "日期不能为空")
	}

	// 验证日期格式
	_, err := time.Parse("2006-01-02", in.Date)
	if err != nil {
		return nil, gerror.NewCode(CodeNutritionInvalidDate, "日期格式无效")
	}

	// 获取当日所有用户的饮食记录
	activeUsers, err := service.Diet().GetActiveUserIds(ctx, &model.DietRecordActiveUsersInput{
		Date: &in.Date,
	})
	if err != nil {
		return nil, gerror.NewCode(CodeNutritionCalculationFailed, "计算达标率失败")
	}

	if len(activeUsers.UserIds) == 0 {
		return &model.NutritionComplianceRateOutput{
			ComplianceRate: 0,
		}, nil
	}

	// 计算每个用户的营养达标情况
	var compliantUsers int
	for _, userId := range activeUsers.UserIds {
		stat, err := s.GetDailyNutritionStat(ctx, &model.NutritionDailyStatInput{
			UserId: userId,
			Date:   in.Date,
		})
		if err != nil {
			continue
		}

		// 判断是否达标（所有营养素达成率在80%-120%之间）
		if s.isNutritionCompliant(stat.Stat) {
			compliantUsers++
		}
	}

	complianceRate := (float64(compliantUsers) / float64(len(activeUsers.UserIds))) * 100

	return &model.NutritionComplianceRateOutput{
		ComplianceRate: complianceRate,
	}, nil
}

// GetAllNutritionTrend 获取所有用户营养趋势(供仪表盘使用)
func (s *sNutrition) GetAllNutritionTrend(ctx context.Context, in *model.NutritionAllTrendInput) (*model.NutritionAllTrendOutput, error) {
	if in.Period == "" {
		return nil, gerror.NewCode(CodeNutritionInvalidPeriod, "时间周期不能为空")
	}

	// 计算日期范围
	now := time.Now()
	var startDate, endDate time.Time

	switch in.Period {
	case "week":
		startDate = now.AddDate(0, 0, -7)
		endDate = now
	case "month":
		startDate = now.AddDate(0, -1, 0)
		endDate = now
	case "year":
		startDate = now.AddDate(-1, 0, 0)
		endDate = now
	default:
		return nil, gerror.NewCode(CodeNutritionInvalidPeriod, "不支持的时间周期")
	}

	// 获取活跃用户列表
	startDateStr := startDate.Format("2006-01-02")
	endDateStr := endDate.Format("2006-01-02")
	activeUsers, err := service.Diet().GetActiveUserIds(ctx, &model.DietRecordActiveUsersInput{
		StartDate: &startDateStr,
		EndDate:   &endDateStr,
	})
	if err != nil {
		return nil, gerror.NewCode(CodeNutritionCalculationFailed, "计算营养趋势失败")
	}

	if len(activeUsers.UserIds) == 0 {
		return &model.NutritionAllTrendOutput{
			DateList:    []string{},
			CalorieList: []float64{},
			ProteinList: []float64{},
			CarbsList:   []float64{},
			FatList:     []float64{},
		}, nil
	}

	// 批量获取饮食记录
	batchRecords, err := service.Diet().GetBatchDietRecordsForNutritionStat(ctx, &model.DietRecordBatchQueryInput{
		UserIds:   activeUsers.UserIds,
		StartDate: startDate.Format("2006-01-02"),
		EndDate:   endDate.Format("2006-01-02"),
	})
	if err != nil {
		return nil, gerror.NewCode(CodeNutritionCalculationFailed, "计算营养趋势失败")
	}

	// 生成日期列表并计算平均值
	var dateList []string
	var calorieList []float64
	var proteinList []float64
	var carbsList []float64
	var fatList []float64

	current := startDate
	for !current.After(endDate) {
		dateStr := current.Format("2006-01-02")
		dateList = append(dateList, dateStr)

		// 计算当日所有用户的平均营养摄入
		var totalCalorie, totalProtein, totalCarbs, totalFat float64
		var userCount int

		for _, userId := range activeUsers.UserIds {
			if userRecords, exists := batchRecords.Records[userId]; exists {
				if dayRecords, exists := userRecords[dateStr]; exists {
					var userCalorie, userProtein, userCarbs, userFat float64
					for _, record := range dayRecords {
						for _, food := range record.Foods {
							userCalorie += food.Calories
							userProtein += food.Protein
							userCarbs += food.Carbs
							userFat += food.Fat
						}
					}
					totalCalorie += userCalorie
					totalProtein += userProtein
					totalCarbs += userCarbs
					totalFat += userFat
					userCount++
				}
			}
		}

		// 计算平均值
		var avgCalorie, avgProtein, avgCarbs, avgFat float64
		if userCount > 0 {
			avgCalorie = totalCalorie / float64(userCount)
			avgProtein = totalProtein / float64(userCount)
			avgCarbs = totalCarbs / float64(userCount)
			avgFat = totalFat / float64(userCount)
		}

		calorieList = append(calorieList, avgCalorie)
		proteinList = append(proteinList, avgProtein)
		carbsList = append(carbsList, avgCarbs)
		fatList = append(fatList, avgFat)

		current = current.AddDate(0, 0, 1)
	}

	return &model.NutritionAllTrendOutput{
		DateList:    dateList,
		CalorieList: calorieList,
		ProteinList: proteinList,
		CarbsList:   carbsList,
		FatList:     fatList,
	}, nil
}

// generateAdviceByPercentage 根据营养素达成百分比生成建议
func (s *sNutrition) generateAdviceByPercentage(nutrientType string, percentage float64) []model.NutritionAdviceDisplay {
	var advices []model.NutritionAdviceDisplay

	switch nutrientType {
	case "protein":
		if percentage < 80 {
			advices = append(advices, model.NutritionAdviceDisplay{
				Type:        "warning",
				Title:       "蛋白质摄入不足",
				Description: "建议增加优质蛋白质食物，如鸡蛋、瘦肉、豆类等",
			})
		} else if percentage > 120 {
			advices = append(advices, model.NutritionAdviceDisplay{
				Type:        "info",
				Title:       "蛋白质摄入过量",
				Description: "适量减少蛋白质摄入，注意营养均衡",
			})
		}
	case "carbs":
		if percentage < 80 {
			advices = append(advices, model.NutritionAdviceDisplay{
				Type:        "warning",
				Title:       "碳水化合物摄入不足",
				Description: "建议增加全谷物、薯类等优质碳水化合物",
			})
		} else if percentage > 120 {
			advices = append(advices, model.NutritionAdviceDisplay{
				Type:        "danger",
				Title:       "碳水化合物摄入过量",
				Description: "减少精制糖和加工食品，选择复合碳水化合物",
			})
		}
	case "fat":
		if percentage < 80 {
			advices = append(advices, model.NutritionAdviceDisplay{
				Type:        "info",
				Title:       "脂肪摄入不足",
				Description: "适量增加健康脂肪，如坚果、橄榄油、鱼类等",
			})
		} else if percentage > 120 {
			advices = append(advices, model.NutritionAdviceDisplay{
				Type:        "warning",
				Title:       "脂肪摄入过量",
				Description: "减少油炸食品和高脂肪食物，选择健康脂肪来源",
			})
		}
	case "calorie":
		if percentage < 80 {
			advices = append(advices, model.NutritionAdviceDisplay{
				Type:        "warning",
				Title:       "热量摄入不足",
				Description: "增加营养密度高的食物，确保能量充足",
			})
		} else if percentage > 120 {
			advices = append(advices, model.NutritionAdviceDisplay{
				Type:        "danger",
				Title:       "热量摄入过量",
				Description: "控制食物分量，增加运动消耗",
			})
		}
	}

	return advices
}

// isNutritionCompliant 判断营养摄入是否达标
func (s *sNutrition) isNutritionCompliant(stat model.NutritionStat) bool {
	// 所有营养素达成率在80%-120%之间认为达标
	return stat.CaloriePercentage >= 80 && stat.CaloriePercentage <= 120 &&
		stat.ProteinPercentage >= 80 && stat.ProteinPercentage <= 120 &&
		stat.CarbsPercentage >= 80 && stat.CarbsPercentage <= 120 &&
		stat.FatPercentage >= 80 && stat.FatPercentage <= 120
}
