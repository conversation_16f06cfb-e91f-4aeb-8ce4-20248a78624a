package service

import (
	"context"
	"shikeyinxiang-goframe/internal/model"
)

type IFile interface {
	// GenerateUploadUrl 生成文件上传预签名URL
	GenerateUploadUrl(ctx context.Context, in *model.FileUploadInput) (*model.FileUploadOutput, error)

	// GenerateDownloadUrl 生成文件下载预签名URL
	GenerateDownloadUrl(ctx context.Context, in *model.PreSignedUrlInput) (*model.PreSignedUrlOutput, error)

	// DeleteFile 删除文件
	DeleteFile(ctx context.Context, in *model.FileDeleteInput) (*model.FileDeleteOutput, error)
}

var localFile IFile

func File() IFile {
	if localFile == nil {
		panic("implement not found for interface IFile, forgot register?")
	}
	return localFile
}

func RegisterFile(i IFile) {
	localFile = i
}
