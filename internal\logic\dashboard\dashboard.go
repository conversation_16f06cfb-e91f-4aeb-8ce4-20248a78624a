package dashboard

import (
	"context"
	"runtime"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/gtime"

	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

type sDashboard struct{}

func init() {
	service.RegisterDashboard(New())
}

func New() service.IDashboard {
	return &sDashboard{}
}

// GetDashboardStats 获取仪表盘统计数据
func (s *sDashboard) GetDashboardStats(ctx context.Context, in *model.DashboardStatsInput) (*model.DashboardStatsOutput, error) {
	// 设置默认日期为今天
	queryDate := in.Date
	if queryDate == "" {
		queryDate = gtime.Now().Format("2006-01-02")
	}

	// 验证日期格式
	_, err := time.Parse("2006-01-02", queryDate)
	if err != nil {
		return nil, gerror.NewCode(CodeDashboardInvalidDateRange, "日期格式无效")
	}

	var stats model.DashboardStats

	// 获取总用户数
	totalUsers, err := service.User().GetTotalUserCount(ctx)
	if err != nil {
		return nil, gerror.NewCode(CodeDashboardStatsCalculationFailed, "获取统计数据失败")
	}
	stats.TotalUsers = totalUsers

	// 获取指定日期的饮食记录数
	todayRecords, err := service.Diet().CountDietRecordsByDate(ctx, &model.DietRecordCountByDateInput{
		Date: queryDate,
	})
	if err != nil {
		return nil, gerror.NewCode(CodeDashboardStatsCalculationFailed, "获取统计数据失败")
	}
	stats.TodayRecords = todayRecords.Count

	// 计算营养达标率
	complianceRate, err := service.Nutrition().CalculateNutritionComplianceRate(ctx, &model.NutritionComplianceRateInput{
		Date: queryDate,
	})
	if err != nil {
		return nil, gerror.NewCode(CodeDashboardStatsCalculationFailed, "获取统计数据失败")
	}
	stats.NutritionComplianceRate = complianceRate.ComplianceRate

	// 推荐准确率（模拟数据）
	stats.RecommendationAccuracy = 95

	// 设置统计日期
	stats.StatisticsDate = queryDate

	// 获取活跃用户数（当日有饮食记录的用户）
	activeUsers, err := service.Diet().GetActiveUserIds(ctx, &model.DietRecordActiveUsersInput{
		Date: &queryDate,
	})
	if err != nil {
		stats.ActiveUsers = 0
	} else {
		stats.ActiveUsers = int64(len(activeUsers.UserIds))
	}

	// 获取本周新增用户数（简化实现，暂时设为0）
	stats.WeeklyNewUsers = 0

	// 获取本月新增用户数（简化实现，暂时设为0）
	stats.MonthlyNewUsers = 0

	return &model.DashboardStatsOutput{
		Stats: stats,
	}, nil
}

// GetNutritionTrend 获取营养摄入趋势
func (s *sDashboard) GetNutritionTrend(ctx context.Context, in *model.DashboardNutritionTrendInput) (*model.DashboardNutritionTrendOutput, error) {
	if in.Period == "" {
		return nil, gerror.NewCode(CodeDashboardInvalidDateRange, "时间周期不能为空")
	}

	// 调用营养分析服务获取所有用户营养趋势
	trendResult, err := service.Nutrition().GetAllNutritionTrend(ctx, &model.NutritionAllTrendInput{
		Period: in.Period,
	})
	if err != nil {
		return nil, gerror.NewCode(CodeDashboardStatsCalculationFailed, "获取营养趋势失败")
	}

	trend := model.DashboardNutritionTrend{
		DateList:    trendResult.DateList,
		CalorieList: trendResult.CalorieList,
		ProteinList: trendResult.ProteinList,
		CarbsList:   trendResult.CarbsList,
		FatList:     trendResult.FatList,
		Period:      in.Period,
		DataPoints:  len(trendResult.DateList),
	}

	return &model.DashboardNutritionTrendOutput{
		Trend: trend,
	}, nil
}

// GetLatestDietRecords 获取最新饮食记录列表
func (s *sDashboard) GetLatestDietRecords(ctx context.Context, in *model.DashboardLatestDietRecordsInput) (*model.DashboardLatestDietRecordsOutput, error) {
	// 设置默认分页参数
	if in.Page <= 0 {
		in.Page = 1
	}
	if in.Size <= 0 {
		in.Size = 10
	}

	// 调用饮食记录服务获取所有用户记录
	records, err := service.Diet().GetAllUsersDietRecords(ctx, &model.DietRecordAdminQueryInput{
		UserId:    in.UserId,
		StartDate: in.StartDate,
		EndDate:   in.EndDate,
		MealType:  in.MealType,
		Page:      in.Page,
		Size:      in.Size,
	})
	if err != nil {
		return nil, gerror.NewCode(CodeDashboardStatsCalculationFailed, "获取饮食记录失败")
	}

	return &model.DashboardLatestDietRecordsOutput{
		List:  records.List,
		Page:  records.Page,
		Size:  records.Size,
		Total: records.Total,
	}, nil
}

// GetDietRecordDetail 获取饮食记录详情
func (s *sDashboard) GetDietRecordDetail(ctx context.Context, in *model.DashboardDietRecordDetailInput) (*model.DashboardDietRecordDetailOutput, error) {
	if in.RecordId <= 0 {
		return nil, gerror.NewCode(CodeDashboardDataNotFound, "记录ID无效")
	}

	// 调用饮食记录服务获取详情（管理员权限，不需要验证用户ID）
	detail, err := service.Diet().GetDietRecordDetail(ctx, &model.DietRecordDetailInput{
		RecordId: in.RecordId,
		UserId:   nil, // 管理员查询，不限制用户ID
	})
	if err != nil {
		return nil, gerror.NewCode(CodeDashboardDataNotFound, "获取记录详情失败")
	}

	return &model.DashboardDietRecordDetailOutput{
		Record: detail.Record,
	}, nil
}

// GetPopularFoods 获取热门食物统计
func (s *sDashboard) GetPopularFoods(ctx context.Context, in *model.DashboardPopularFoodsInput) (*model.DashboardPopularFoodsOutput, error) {
	if in.Period == "" {
		return nil, gerror.NewCode(CodeDashboardInvalidDateRange, "时间周期不能为空")
	}
	if in.Limit <= 0 {
		in.Limit = 10
	}

	// 调用饮食记录服务获取热门食物
	popularFoods, err := service.Diet().GetPopularFoods(ctx, &model.DietRecordPopularFoodsInput{
		Period: in.Period,
		Limit:  in.Limit,
	})
	if err != nil {
		return nil, gerror.NewCode(CodeDashboardStatsCalculationFailed, "获取热门食物失败")
	}

	// 转换为仪表盘格式
	var foods []model.PopularFood
	for i, food := range popularFoods.Foods {
		foods = append(foods, model.PopularFood{
			FoodId:       0, // 暂时没有食物ID
			FoodName:     food.FoodName,
			ImageUrl:     "", // 暂时没有图片URL
			UsageCount:   int64(food.Count),
			UserCount:    0, // 暂时没有用户数统计
			Rank:         i + 1,
			Calorie:      0,  // 暂时没有热量信息
			CategoryName: "", // 暂时没有分类信息
		})
	}

	return &model.DashboardPopularFoodsOutput{
		Foods: foods,
	}, nil
}

// GetUserStats 获取用户统计数据
func (s *sDashboard) GetUserStats(ctx context.Context, in *model.DashboardUserStatsInput) (*model.DashboardUserStatsOutput, error) {
	// 获取总用户数
	totalUsers, err := service.User().GetTotalUserCount(ctx)
	if err != nil {
		return nil, gerror.NewCode(CodeDashboardStatsCalculationFailed, "获取用户统计失败")
	}

	// 简化实现，其他统计数据暂时使用模拟值
	return &model.DashboardUserStatsOutput{
		TotalUsers:     totalUsers,
		ActiveUsers:    totalUsers / 2, // 假设50%活跃
		NewUsers:       0,              // 暂时没有新增用户统计
		RetentionRate:  85.5,           // 模拟留存率
		EngagementRate: 72.3,           // 模拟参与率
	}, nil
}

// GetSystemHealth 获取系统健康状态
func (s *sDashboard) GetSystemHealth(ctx context.Context, in *model.DashboardSystemHealthInput) (*model.DashboardSystemHealthOutput, error) {
	// 获取系统运行时信息
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// 计算内存使用率（简化计算）
	memoryUsage := float64(m.Alloc) / float64(m.Sys) * 100

	return &model.DashboardSystemHealthOutput{
		Status:         "healthy",
		Uptime:         int64(time.Since(time.Now().Add(-24 * time.Hour)).Seconds()), // 模拟24小时运行时间
		CpuUsage:       15.5,                                                         // 模拟CPU使用率
		MemoryUsage:    memoryUsage,
		DiskUsage:      45.2,        // 模拟磁盘使用率
		DatabaseStatus: "connected", // 模拟数据库状态
		CacheStatus:    "connected", // 模拟缓存状态
	}, nil
}
