package file

import "github.com/gogf/gf/v2/errors/gcode"

// 文件模块错误码定义
// 错误码格式：AABBBCCC
// AA: 40 (文件模块)
// BBB: 001-999 (功能模块)
// CCC: 001-999 (具体错误)

var (
	// 文件基础操作错误 (40001xxx)
	CodeFileNotFound      = gcode.New(40001001, "文件不存在", nil)
	CodeFileUploadFailed  = gcode.New(40001002, "文件上传失败", nil)
	CodeFileDeleteFailed  = gcode.New(40001003, "文件删除失败", nil)

	// 文件验证相关错误 (40002xxx)
	CodeFileTooBig         = gcode.New(40002001, "文件大小超出限制", nil)
	CodeFileTypeNotAllowed = gcode.New(40002002, "不支持的文件类型", nil)
	CodeFileNameInvalid    = gcode.New(40002003, "文件名无效", nil)

	// 存储服务相关错误 (40003xxx)
	CodeFileStorageError = gcode.New(40003001, "存储服务错误", nil)
	CodeFileUrlExpired   = gcode.New(40003002, "文件URL已过期", nil)

	// 权限相关错误 (40004xxx)
	CodeFilePermissionDenied = gcode.New(40004001, "文件访问权限不足", nil)

	// 安全相关错误 (40005xxx)
	CodeFileVirusDetected = gcode.New(40005001, "检测到恶意文件", nil)
)
