package nutrition

import (
	"context"

	"shikeyinxiang-goframe/api/nutrition/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) ComplianceRate(ctx context.Context, req *v1.ComplianceRateReq) (res *v1.ComplianceRateRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.ComplianceRateInput{
		UserId:    req.UserId,
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
	}

	// 2. 调用业务逻辑
	output, err := service.Nutrition().CalculateNutritionComplianceRate(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.ComplianceRateRes{
		ComplianceRate: output.ComplianceRate,
		Details:        output.Details,
	}

	return res, nil
}
