package user

import (
	"context"

	"shikeyinxiang-goframe/api/user/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) CurrentUserUpdate(ctx context.Context, req *v1.CurrentUserUpdateReq) (res *v1.CurrentUserUpdateRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.CurrentUserUpdateInput{
		UserId:   req.UserId,
		Nickname: req.Nickname,
		Avatar:   req.Avatar,
		Gender:   req.Gender,
		Age:      req.Age,
		Height:   req.Height,
		Weight:   req.Weight,
	}

	// 2. 调用业务逻辑
	output, err := service.User().UpdateCurrentUser(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.CurrentUserUpdateRes{
		Success: output.Success,
		Message: output.Message,
	}

	return res, nil
}
