package user

import (
	"context"

	"shikeyinxiang-goframe/api/user/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) AvatarUploadUrl(ctx context.Context, req *v1.AvatarUploadUrlReq) (res *v1.AvatarUploadUrlRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.AvatarUploadUrlInput{
		UserId:   req.UserId,
		FileName: req.FileName,
		FileSize: req.FileSize,
	}

	// 2. 调用业务逻辑
	output, err := service.User().GetAvatarUploadUrl(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.AvatarUploadUrlRes{
		UploadUrl: output.UploadUrl,
		FileKey:   output.FileKey,
	}

	return res, nil
}
