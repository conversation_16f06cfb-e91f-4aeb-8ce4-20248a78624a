package nutrition

import (
	"context"

	"shikeyinxiang-goframe/api/nutrition/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) NutritionGoalSet(ctx context.Context, req *v1.NutritionGoalSetReq) (res *v1.NutritionGoalSetRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.NutritionGoalSetInput{
		UserId:          req.UserId,
		CaloriesGoal:    req.CaloriesGoal,
		ProteinGoal:     req.ProteinGoal,
		FatGoal:         req.FatGoal,
		CarbsGoal:       req.CarbsGoal,
		FiberGoal:       req.FiberGoal,
		SugarGoal:       req.SugarGoal,
		SodiumGoal:      req.SodiumGoal,
		ActivityLevel:   req.ActivityLevel,
		WeightGoal:      req.WeightGoal,
		HealthCondition: req.HealthCondition,
	}

	// 2. 调用业务逻辑
	output, err := service.Nutrition().SetNutritionGoal(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.NutritionGoalSetRes{
		Success: output.Success,
		Message: output.Message,
	}

	return res, nil
}
