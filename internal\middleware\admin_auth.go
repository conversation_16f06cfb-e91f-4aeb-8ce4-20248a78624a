package middleware

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"

	"shikeyinxiang-goframe/internal/consts"
)

// AdminAuth 管理员授权中间件
func AdminAuth(r *ghttp.Request) {
	// 1. 从上下文获取用户信息
	userInfo := r.Context().Value(consts.CtxUserInfo)
	if userInfo == nil {
		r.Response.WriteJsonExit(g.Map{
			"code":    401,
			"message": "用户未认证",
			"data":    nil,
		})
		return
	}

	// 2. 类型断言获取用户信息
	user, ok := userInfo.(*UserInfo)
	if !ok {
		r.Response.WriteJsonExit(g.Map{
			"code":    401,
			"message": "用户信息格式错误",
			"data":    nil,
		})
		return
	}

	// 3. 验证管理员角色
	if user.Role != "ADMIN" {
		r.Response.WriteJsonExit(g.Map{
			"code":    403,
			"message": "权限不足，需要管理员权限",
			"data":    nil,
		})
		return
	}

	// 管理员不需要检查激活状态，直接继续执行
	r.Middleware.Next()
}
