package user

import (
	"context"

	"shikeyinxiang-goframe/api/user/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) UserList(ctx context.Context, req *v1.UserListReq) (res *v1.UserListRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.UserListInput{
		Page:     req.Page,
		PageSize: req.PageSize,
		Username: req.Username,
		Email:    req.Email,
		Status:   req.Status,
	}

	// 2. 调用业务逻辑
	output, err := service.User().GetUserList(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.UserListRes{
		List:     output.List,
		Total:    output.Total,
		Page:     output.Page,
		PageSize: output.PageSize,
	}

	return res, nil
}
