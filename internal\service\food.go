package service

import (
	"context"
	"shikeyinxiang-goframe/internal/model"
)

type IFood interface {
	// GetFoodList 分页查询食物列表
	GetFoodList(ctx context.Context, in *model.FoodQueryInput) (*model.FoodListOutput, error)
	
	// GetFoodByID 根据ID获取食物详情
	GetFoodByID(ctx context.Context, id int) (*model.FoodInfo, error)
	
	// CreateFood 创建食物
	CreateFood(ctx context.Context, in *model.FoodCreateInput) (*model.FoodCreateOutput, error)
	
	// UpdateFood 更新食物信息
	UpdateFood(ctx context.Context, in *model.FoodUpdateInput) (*model.FoodUpdateOutput, error)
	
	// DeleteFood 删除食物
	DeleteFood(ctx context.Context, id int) error
	
	// UpdateFoodImage 更新食物图片
	UpdateFoodImage(ctx context.Context, in *model.FoodImageUpdateInput) (*model.FoodImageUpdateOutput, error)
	
	// BatchImportFoods 批量导入食物
	BatchImportFoods(ctx context.Context, in *model.FoodBatchImportInput) (*model.FoodBatchImportOutput, error)
	
	// GetAllCategories 获取所有分类名称列表
	GetAllCategories(ctx context.Context) ([]string, error)
}

type IFoodCategory interface {
	// GetAllCategories 获取所有食物分类
	GetAllCategories(ctx context.Context) ([]*model.FoodCategoryInfo, error)
	
	// GetCategoryList 分页查询食物分类
	GetCategoryList(ctx context.Context, current, size int) (*model.FoodCategoryListOutput, error)
	
	// GetCategoryByID 根据ID获取食物分类
	GetCategoryByID(ctx context.Context, id int) (*model.FoodCategoryInfo, error)
	
	// CreateCategory 创建食物分类
	CreateCategory(ctx context.Context, in *model.FoodCategoryCreateInput) (*model.FoodCategoryCreateOutput, error)
	
	// UpdateCategory 更新食物分类
	UpdateCategory(ctx context.Context, in *model.FoodCategoryUpdateInput) (*model.FoodCategoryUpdateOutput, error)
	
	// DeleteCategory 删除食物分类
	DeleteCategory(ctx context.Context, id int) error
}

var localFood IFood
var localFoodCategory IFoodCategory

func Food() IFood {
	if localFood == nil {
		panic("implement not found for interface IFood, forgot register?")
	}
	return localFood
}

func RegisterFood(i IFood) {
	localFood = i
}

func FoodCategory() IFoodCategory {
	if localFoodCategory == nil {
		panic("implement not found for interface IFoodCategory, forgot register?")
	}
	return localFoodCategory
}

func RegisterFoodCategory(i IFoodCategory) {
	localFoodCategory = i
}
