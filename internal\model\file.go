package model

// FileUploadInput 文件上传输入参数
type FileUploadInput struct {
	UserId      int64  `json:"userId" v:"required" dc:"用户ID"`
	FileType    string `json:"fileType" v:"required" dc:"文件类型"`
	ContentType string `json:"contentType" v:"required" dc:"文件内容类型"`
	Expiration  int    `json:"expiration" dc:"URL有效期（分钟）"`
}

// FileUploadOutput 文件上传输出结果
type FileUploadOutput struct {
	UploadUrl string `json:"uploadUrl" dc:"上传URL"`
	FileName  string `json:"fileName" dc:"文件名"`
}

// FileDeleteInput 文件删除输入参数
type FileDeleteInput struct {
	FileName string `json:"fileName" v:"required" dc:"文件名"`
}

// FileDeleteOutput 文件删除输出结果
type FileDeleteOutput struct {
	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"结果消息"`
}

// PreSignedUrlInput 预签名URL生成输入参数
type PreSignedUrlInput struct {
	FileName   string `json:"fileName" v:"required" dc:"文件名"`
	Expiration int    `json:"expiration" dc:"URL有效期（分钟）"`
}

// PreSignedUrlOutput 预签名URL生成输出结果
type PreSignedUrlOutput struct {
	Url string `json:"url" dc:"预签名URL"`
}

// FileStorageConfig 文件存储配置
type FileStorageConfig struct {
	Provider          string `json:"provider" dc:"存储提供商"`
	BucketName        string `json:"bucketName" dc:"存储桶名称"`
	Region            string `json:"region" dc:"区域"`
	Endpoint          string `json:"endpoint" dc:"端点"`
	AccessKey         string `json:"accessKey" dc:"访问密钥"`
	SecretKey         string `json:"secretKey" dc:"密钥"`
	CDNUrl            string `json:"cdnUrl" dc:"CDN URL"`
	DefaultExpiration int    `json:"defaultExpiration" dc:"默认过期时间（分钟）"`
}
