package food

import (
	"context"

	"shikeyinxiang-goframe/api/food/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) CategoryList(ctx context.Context, req *v1.CategoryListReq) (res *v1.CategoryListRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.CategoryListInput{
		Page:     req.Page,
		PageSize: req.PageSize,
		Name:     req.Name,
	}

	// 2. 调用业务逻辑
	output, err := service.FoodCategory().GetCategoryList(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.CategoryListRes{
		List:     output.List,
		Total:    output.Total,
		Page:     output.Page,
		PageSize: output.PageSize,
	}

	return res, nil
}
