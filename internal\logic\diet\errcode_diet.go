package diet

import "github.com/gogf/gf/v2/errors/gcode"

// 饮食记录模块错误码定义
// 错误码格式：AABBBCCC (AA=50饮食模块, BBB=001-999功能, CCC=001-999具体错误)

// ==================== 饮食记录基础错误 (50001xxx) ====================

var (
	// CodeDietRecordNotFound 饮食记录不存在
	CodeDietRecordNotFound = gcode.New(50001001, "饮食记录不存在", nil)
	
	// CodeDietRecordExists 饮食记录已存在
	CodeDietRecordExists = gcode.New(50001002, "饮食记录已存在", nil)
	
	// CodeDietRecordInvalidDate 记录日期无效
	CodeDietRecordInvalidDate = gcode.New(50001003, "记录日期无效", nil)
	
	// CodeDietRecordInvalidTime 记录时间无效
	CodeDietRecordInvalidTime = gcode.New(50001004, "记录时间无效", nil)
	
	// CodeDietRecordInvalidUser 用户ID无效
	CodeDietRecordInvalidUser = gcode.New(50001005, "用户ID无效", nil)
	
	// CodeDietRecordCreateFailed 创建饮食记录失败
	CodeDietRecordCreateFailed = gcode.New(50001006, "创建饮食记录失败", nil)
	
	// CodeDietRecordUpdateFailed 更新饮食记录失败
	CodeDietRecordUpdateFailed = gcode.New(50001007, "更新饮食记录失败", nil)
	
	// CodeDietRecordDeleteFailed 删除饮食记录失败
	CodeDietRecordDeleteFailed = gcode.New(50001008, "删除饮食记录失败", nil)
	
	// CodeDietRecordQueryFailed 查询饮食记录失败
	CodeDietRecordQueryFailed = gcode.New(50001009, "查询饮食记录失败", nil)
)

// ==================== 餐次和食物相关错误 (50002xxx) ====================

var (
	// CodeDietMealTypeInvalid 餐次类型无效
	CodeDietMealTypeInvalid = gcode.New(50002001, "餐次类型无效", nil)
	
	// CodeDietFoodNotFound 食物不存在
	CodeDietFoodNotFound = gcode.New(50002002, "食物不存在", nil)
	
	// CodeDietQuantityInvalid 食物数量无效
	CodeDietQuantityInvalid = gcode.New(50002003, "食物数量无效", nil)
	
	// CodeDietCalorieInvalid 热量计算错误
	CodeDietCalorieInvalid = gcode.New(50002004, "热量计算错误", nil)
	
	// CodeDietFoodListEmpty 食物列表为空
	CodeDietFoodListEmpty = gcode.New(50002005, "食物列表不能为空", nil)
	
	// CodeDietFoodDataInvalid 食物数据无效
	CodeDietFoodDataInvalid = gcode.New(50002006, "食物数据无效", nil)
)

// ==================== 权限相关错误 (50003xxx) ====================

var (
	// CodeDietPermissionDenied 无权限访问此记录
	CodeDietPermissionDenied = gcode.New(50003001, "无权限访问此记录", nil)
	
	// CodeDietUserMismatch 用户不匹配
	CodeDietUserMismatch = gcode.New(50003002, "用户不匹配", nil)
	
	// CodeDietAdminRequired 需要管理员权限
	CodeDietAdminRequired = gcode.New(50003003, "需要管理员权限", nil)
)

// ==================== 统计分析相关错误 (50004xxx) ====================

var (
	// CodeDietStatsCalculationFailed 统计计算失败
	CodeDietStatsCalculationFailed = gcode.New(50004001, "统计计算失败", nil)
	
	// CodeDietStatsDateRangeInvalid 统计日期范围无效
	CodeDietStatsDateRangeInvalid = gcode.New(50004002, "统计日期范围无效", nil)
	
	// CodeDietStatsParameterInvalid 统计参数无效
	CodeDietStatsParameterInvalid = gcode.New(50004003, "统计参数无效", nil)
	
	// CodeDietStatsDataNotFound 统计数据不存在
	CodeDietStatsDataNotFound = gcode.New(50004004, "统计数据不存在", nil)
)

// ==================== 批量操作相关错误 (50005xxx) ====================

var (
	// CodeDietBatchQueryFailed 批量查询失败
	CodeDietBatchQueryFailed = gcode.New(50005001, "批量查询失败", nil)
	
	// CodeDietBatchUserIdsEmpty 用户ID列表为空
	CodeDietBatchUserIdsEmpty = gcode.New(50005002, "用户ID列表不能为空", nil)
	
	// CodeDietBatchParameterInvalid 批量操作参数无效
	CodeDietBatchParameterInvalid = gcode.New(50005003, "批量操作参数无效", nil)
)
