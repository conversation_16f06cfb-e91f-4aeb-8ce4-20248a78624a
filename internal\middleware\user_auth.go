package middleware

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"

	"shikeyinxiang-goframe/internal/consts"
	"shikeyinxiang-goframe/internal/service"
)

// UserAuth 用户授权中间件
func UserAuth(r *ghttp.Request) {
	// 1. 从上下文获取用户信息
	userInfo := r.Context().Value(consts.CtxUserInfo)
	if userInfo == nil {
		r.Response.WriteJsonExit(g.Map{
			"code":    401,
			"message": "用户未认证",
			"data":    nil,
		})
		return
	}

	// 2. 类型断言获取用户信息
	user, ok := userInfo.(*UserInfo)
	if !ok {
		r.Response.WriteJsonExit(g.Map{
			"code":    401,
			"message": "用户信息格式错误",
			"data":    nil,
		})
		return
	}

	// 3. 验证用户角色
	if user.Role != "USER" {
		r.Response.WriteJsonExit(g.Map{
			"code":    403,
			"message": "权限不足，需要用户权限",
			"data":    nil,
		})
		return
	}

	// 4. 验证用户状态（检查是否被封禁）
	userDetail, err := service.User().GetUserByID(r.Context(), user.UserId)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code":    500,
			"message": "获取用户信息失败",
			"data":    nil,
		})
		return
	}

	if userDetail == nil {
		r.Response.WriteJsonExit(g.Map{
			"code":    401,
			"message": "用户不存在",
			"data":    nil,
		})
		return
	}

	if userDetail.Status == 0 {
		r.Response.WriteJsonExit(g.Map{
			"code":    403,
			"message": "账号已被封禁，请联系管理员",
			"data":    nil,
		})
		return
	}

	// 继续执行
	r.Middleware.Next()
}
