package service

import (
	"context"
	"shikeyinxiang-goframe/internal/model"
)

// IDiet 饮食记录服务接口
type IDiet interface {
	// CreateDietRecord 添加饮食记录
	CreateDietRecord(ctx context.Context, in *model.DietRecordCreateInput) (*model.DietRecordCreateOutput, error)

	// GetDietRecords 获取用户饮食记录列表
	GetDietRecords(ctx context.Context, in *model.DietRecordQueryInput) (*model.DietRecordQueryOutput, error)

	// GetAllUsersDietRecords 获取所有用户饮食记录列表(管理员)
	GetAllUsersDietRecords(ctx context.Context, in *model.DietRecordAdminQueryInput) (*model.DietRecordAdminQueryOutput, error)

	// GetDietRecordDetail 获取饮食记录详情
	GetDietRecordDetail(ctx context.Context, in *model.DietRecordDetailInput) (*model.DietRecordDetailOutput, error)

	// DeleteDietRecord 删除饮食记录
	DeleteDietRecord(ctx context.Context, in *model.DietRecordDeleteInput) (*model.DietRecordDeleteOutput, error)

	// CountDietRecordsByDate 统计指定日期的记录数量
	CountDietRecordsByDate(ctx context.Context, in *model.DietRecordCountByDateInput) (*model.DietRecordCountByDateOutput, error)

	// GetActiveUserIds 获取活跃用户ID列表
	GetActiveUserIds(ctx context.Context, in *model.DietRecordActiveUsersInput) (*model.DietRecordActiveUsersOutput, error)

	// GetPopularFoods 获取热门食物统计
	GetPopularFoods(ctx context.Context, in *model.DietRecordPopularFoodsInput) (*model.DietRecordPopularFoodsOutput, error)

	// GetBatchDietRecordsForNutritionStat 批量获取饮食记录(供营养统计使用)
	GetBatchDietRecordsForNutritionStat(ctx context.Context, in *model.DietRecordBatchQueryInput) (*model.DietRecordBatchQueryOutput, error)
}

var (
	localDiet IDiet
)

// RegisterDiet 注册饮食记录服务
func RegisterDiet(i IDiet) {
	localDiet = i
}

// Diet 获取饮食记录服务
func Diet() IDiet {
	if localDiet == nil {
		panic("implement not found for interface IDiet, forgot register?")
	}
	return localDiet
}
