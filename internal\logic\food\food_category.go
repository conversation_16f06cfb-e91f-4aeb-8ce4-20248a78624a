package food

import (
	"context"
	"strings"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/gtime"

	"shikeyinxiang-goframe/internal/dao"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/model/do"
	"shikeyinxiang-goframe/internal/model/entity"
	"shikeyinxiang-goframe/internal/service"
)

type sFoodCategory struct{}

func init() {
	service.RegisterFoodCategory(NewCategory())
}

func NewCategory() service.IFoodCategory {
	return &sFoodCategory{}
}

// GetAllCategories 获取所有食物分类
func (s *sFoodCategory) GetAllCategories(ctx context.Context) ([]*model.FoodCategoryInfo, error) {
	var categories []*entity.FoodCategory
	err := dao.FoodCategory.Ctx(ctx).OrderAsc(dao.FoodCategory.Columns().SortOrder).Scan(&categories)
	if err != nil {
		return nil, err
	}

	categoryInfos := make([]*model.FoodCategoryInfo, 0, len(categories))
	for _, category := range categories {
		categoryInfo, err := s.convertToCategoryInfo(ctx, category)
		if err != nil {
			return nil, err
		}
		categoryInfos = append(categoryInfos, categoryInfo)
	}

	return categoryInfos, nil
}

// GetCategoryList 分页查询食物分类
func (s *sFoodCategory) GetCategoryList(ctx context.Context, current, size int) (*model.FoodCategoryListOutput, error) {
	// 获取总数
	total, err := dao.FoodCategory.Ctx(ctx).Count()
	if err != nil {
		return nil, err
	}

	// 分页查询
	offset := (current - 1) * size
	var categories []*entity.FoodCategory
	err = dao.FoodCategory.Ctx(ctx).Limit(size).Offset(offset).
		OrderAsc(dao.FoodCategory.Columns().SortOrder).Scan(&categories)
	if err != nil {
		return nil, err
	}

	// 转换为CategoryInfo
	categoryInfos := make([]*model.FoodCategoryInfo, 0, len(categories))
	for _, category := range categories {
		categoryInfo, err := s.convertToCategoryInfo(ctx, category)
		if err != nil {
			return nil, err
		}
		categoryInfos = append(categoryInfos, categoryInfo)
	}

	return &model.FoodCategoryListOutput{
		List:  categoryInfos,
		Total: int64(total),
		Page:  current,
		Size:  size,
	}, nil
}

// GetCategoryByID 根据ID获取食物分类
func (s *sFoodCategory) GetCategoryByID(ctx context.Context, id int) (*model.FoodCategoryInfo, error) {
	var category *entity.FoodCategory
	err := dao.FoodCategory.Ctx(ctx).Where(dao.FoodCategory.Columns().Id, id).Scan(&category)
	if err != nil {
		return nil, err
	}
	if category == nil {
		return nil, gerror.NewCode(CodeFoodCategoryNotFound, "食物分类不存在")
	}

	return s.convertToCategoryInfo(ctx, category)
}

// CreateCategory 创建食物分类
func (s *sFoodCategory) CreateCategory(ctx context.Context, in *model.FoodCategoryCreateInput) (*model.FoodCategoryCreateOutput, error) {
	// 1. 数据验证
	if err := s.validateCategoryCreateInput(ctx, in); err != nil {
		return nil, err
	}

	// 2. 检查分类名称是否已存在
	if err := s.checkCategoryNameExists(ctx, in.Name, 0); err != nil {
		return nil, err
	}

	// 3. 创建分类记录
	now := gtime.Now()
	categoryDO := &do.FoodCategory{
		Name:        in.Name,
		Description: in.Description,
		Color:       in.Color,
		SortOrder:   in.SortOrder,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	result, err := dao.FoodCategory.Ctx(ctx).Data(categoryDO).Insert()
	if err != nil {
		// 处理数据库约束错误
		if strings.Contains(err.Error(), "name") {
			return nil, gerror.NewCode(CodeFoodCategoryExists, "分类名称已存在")
		}
		return nil, err
	}

	// 4. 获取创建的分类ID
	categoryId, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}

	// 5. 返回创建的分类信息
	categoryInfo, err := s.GetCategoryByID(ctx, int(categoryId))
	if err != nil {
		return nil, err
	}

	return &model.FoodCategoryCreateOutput{
		CategoryInfo: categoryInfo,
	}, nil
}

// UpdateCategory 更新食物分类
func (s *sFoodCategory) UpdateCategory(ctx context.Context, in *model.FoodCategoryUpdateInput) (*model.FoodCategoryUpdateOutput, error) {
	// 1. 验证分类是否存在
	existCategory, err := s.GetCategoryByID(ctx, in.Id)
	if err != nil {
		return nil, err
	}
	if existCategory == nil {
		return nil, gerror.NewCode(CodeFoodCategoryNotFound, "食物分类不存在")
	}

	// 2. 检查分类名称是否已被其他分类使用
	if in.Name != "" {
		if err := s.checkCategoryNameExists(ctx, in.Name, in.Id); err != nil {
			return nil, err
		}
	}

	// 3. 构建更新数据
	updateData := &do.FoodCategory{
		UpdatedAt: gtime.Now(),
	}

	if in.Name != "" {
		updateData.Name = in.Name
	}
	if in.Description != "" {
		updateData.Description = in.Description
	}
	if in.Color != "" {
		updateData.Color = in.Color
	}
	if in.SortOrder != 0 {
		updateData.SortOrder = in.SortOrder
	}

	// 4. 执行更新
	_, err = dao.FoodCategory.Ctx(ctx).Where(dao.FoodCategory.Columns().Id, in.Id).Data(updateData).Update()
	if err != nil {
		return &model.FoodCategoryUpdateOutput{
			Success: false,
			Message: "更新失败: " + err.Error(),
		}, nil
	}

	return &model.FoodCategoryUpdateOutput{
		Success: true,
		Message: "更新成功",
	}, nil
}

// DeleteCategory 删除食物分类
func (s *sFoodCategory) DeleteCategory(ctx context.Context, id int) error {
	// 1. 验证分类是否存在
	existCategory, err := s.GetCategoryByID(ctx, id)
	if err != nil {
		return err
	}
	if existCategory == nil {
		return gerror.NewCode(CodeFoodCategoryNotFound, "食物分类不存在")
	}

	// 2. 检查分类是否正在使用中
	foodCount, err := dao.Food.Ctx(ctx).Where(dao.Food.Columns().CategoryId, id).Count()
	if err != nil {
		return err
	}
	if foodCount > 0 {
		return gerror.NewCode(CodeFoodCategoryInUse, "分类正在使用中，无法删除")
	}

	// 3. 删除分类
	_, err = dao.FoodCategory.Ctx(ctx).Where(dao.FoodCategory.Columns().Id, id).Delete()
	return err
}

// convertToCategoryInfo 将entity.FoodCategory转换为model.FoodCategoryInfo
func (s *sFoodCategory) convertToCategoryInfo(ctx context.Context, category *entity.FoodCategory) (*model.FoodCategoryInfo, error) {
	if category == nil {
		return nil, nil
	}

	// 获取该分类下的食物数量
	foodCount, err := dao.Food.Ctx(ctx).Where(dao.Food.Columns().CategoryId, category.Id).Count()
	if err != nil {
		return nil, err
	}

	return &model.FoodCategoryInfo{
		Id:          category.Id,
		Name:        category.Name,
		Description: category.Description,
		Color:       category.Color,
		SortOrder:   int(category.SortOrder),
		FoodCount:   foodCount,
		CreatedAt:   category.CreatedAt,
		UpdatedAt:   category.UpdatedAt,
	}, nil
}

// validateCategoryCreateInput 验证分类创建输入参数
func (s *sFoodCategory) validateCategoryCreateInput(ctx context.Context, in *model.FoodCategoryCreateInput) error {
	if in.Name == "" {
		return gerror.NewCode(CodeFoodCategoryNotFound, "分类名称不能为空")
	}

	return nil
}

// checkCategoryNameExists 检查分类名称是否已被其他分类使用
func (s *sFoodCategory) checkCategoryNameExists(ctx context.Context, name string, excludeCategoryId int) error {
	query := dao.FoodCategory.Ctx(ctx).Where(dao.FoodCategory.Columns().Name, name)
	if excludeCategoryId > 0 {
		query = query.WhereNot(dao.FoodCategory.Columns().Id, excludeCategoryId)
	}

	count, err := query.Count()
	if err != nil {
		return err
	}
	if count > 0 {
		return gerror.NewCode(CodeFoodCategoryExists, "分类名称已存在")
	}
	return nil
}
