package food

import "github.com/gogf/gf/v2/errors/gcode"

// 食物模块错误码定义
// 错误码格式：AABBBCCC
// AA: 30 (食物模块)
// BBB: 001-999 (功能模块)
// CCC: 001-999 (具体错误)

var (
	// 食物基础操作错误 (30001xxx)
	CodeFoodNotFound       = gcode.New(30001001, "食物不存在", nil)
	CodeFoodAlreadyExists  = gcode.New(30001002, "食物已存在", nil)
	CodeFoodNameRequired   = gcode.New(30001003, "食物名称不能为空", nil)
	CodeFoodInvalidNutrition = gcode.New(30001004, "营养成分数据无效", nil)

	// 食物分类相关错误 (30002xxx)
	CodeFoodCategoryNotFound = gcode.New(30002001, "食物分类不存在", nil)
	CodeFoodCategoryExists   = gcode.New(30002002, "食物分类已存在", nil)
	CodeFoodCategoryInUse    = gcode.New(30002003, "分类正在使用中，无法删除", nil)

	// 食物图片相关错误 (30003xxx)
	CodeFoodImageUploadFailed = gcode.New(30003001, "食物图片上传失败", nil)

	// 批量导入相关错误 (30004xxx)
	CodeFoodBatchImportFailed = gcode.New(30004001, "批量导入失败", nil)
	CodeFoodInvalidFormat     = gcode.New(30004002, "导入文件格式错误", nil)
)
