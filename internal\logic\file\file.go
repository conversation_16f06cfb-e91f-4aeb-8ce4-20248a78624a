package file

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/guid"

	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

type sFile struct {
	s3Client    *s3.Client
	s3Presigner *s3.PresignClient
	config      *model.FileStorageConfig
}

func init() {
	service.RegisterFile(New())
}

func New() service.IFile {
	return &sFile{}
}

// initS3Client 初始化S3客户端
func (s *sFile) initS3Client(ctx context.Context) error {
	if s.s3Client != nil {
		return nil
	}

	// 获取配置
	s.config = &model.FileStorageConfig{
		Provider:          g.Cfg().MustGet(ctx, "file.storage.provider").String(),
		BucketName:        g.Cfg().MustGet(ctx, "file.storage.bucketName").String(),
		Region:            g.Cfg().MustGet(ctx, "file.storage.region").String(),
		Endpoint:          g.Cfg().MustGet(ctx, "file.storage.endpoint").String(),
		AccessKey:         g.Cfg().MustGet(ctx, "file.storage.accessKey").String(),
		SecretKey:         g.Cfg().MustGet(ctx, "file.storage.secretKey").String(),
		CDNUrl:            g.Cfg().MustGet(ctx, "file.storage.cdnUrl").String(),
		DefaultExpiration: g.Cfg().MustGet(ctx, "file.storage.defaultExpiration").Int(),
	}

	// 创建AWS配置
	cfg, err := config.LoadDefaultConfig(ctx,
		config.WithRegion(s.config.Region),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(
			s.config.AccessKey,
			s.config.SecretKey,
			"",
		)),
	)
	if err != nil {
		return gerror.NewCode(CodeFileStorageError, "初始化存储配置失败: "+err.Error())
	}

	// 创建S3客户端
	s.s3Client = s3.NewFromConfig(cfg, func(o *s3.Options) {
		o.BaseEndpoint = aws.String(s.config.Endpoint)
		o.UsePathStyle = true
	})

	// 创建预签名客户端
	s.s3Presigner = s3.NewPresignClient(s.s3Client)

	return nil
}

// GenerateUploadUrl 生成文件上传URL
func (s *sFile) GenerateUploadUrl(ctx context.Context, in *model.FileUploadInput) (*model.FileUploadOutput, error) {
	// 初始化S3客户端
	if err := s.initS3Client(ctx); err != nil {
		return nil, err
	}

	// 验证文件类型
	if err := s.validateFileType(ctx, in.ContentType); err != nil {
		return nil, err
	}

	// 生成文件名
	fileName := s.generateFileName(in.UserId, in.FileType, in.ContentType)

	// 设置过期时间
	expiration := in.Expiration
	if expiration <= 0 {
		expiration = s.config.DefaultExpiration
	}

	// 创建预签名上传请求
	putObjectInput := &s3.PutObjectInput{
		Bucket:      aws.String(s.config.BucketName),
		Key:         aws.String(fileName),
		ContentType: aws.String(in.ContentType),
	}

	presignRequest, err := s.s3Presigner.PresignPutObject(ctx, putObjectInput, func(opts *s3.PresignOptions) {
		opts.Expires = time.Duration(expiration) * time.Minute
	})
	if err != nil {
		return nil, gerror.NewCode(CodeFileUploadFailed, "生成上传URL失败: "+err.Error())
	}

	return &model.FileUploadOutput{
		UploadUrl: presignRequest.URL,
		FileName:  fileName,
	}, nil
}

// GenerateDownloadUrl 生成文件下载URL
func (s *sFile) GenerateDownloadUrl(ctx context.Context, in *model.PreSignedUrlInput) (*model.PreSignedUrlOutput, error) {
	// 初始化S3客户端
	if err := s.initS3Client(ctx); err != nil {
		return nil, err
	}

	// 检查文件是否存在
	exists, err := s.checkFileExists(ctx, in.FileName)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, gerror.NewCode(CodeFileNotFound, "文件不存在")
	}

	// 设置过期时间
	expiration := in.Expiration
	if expiration <= 0 {
		expiration = s.config.DefaultExpiration
	}

	// 创建预签名下载请求
	getObjectInput := &s3.GetObjectInput{
		Bucket: aws.String(s.config.BucketName),
		Key:    aws.String(in.FileName),
	}

	presignRequest, err := s.s3Presigner.PresignGetObject(ctx, getObjectInput, func(opts *s3.PresignOptions) {
		opts.Expires = time.Duration(expiration) * time.Minute
	})
	if err != nil {
		return nil, gerror.NewCode(CodeFileStorageError, "生成下载URL失败: "+err.Error())
	}

	return &model.PreSignedUrlOutput{
		Url: presignRequest.URL,
	}, nil
}

// DeleteFile 删除文件
func (s *sFile) DeleteFile(ctx context.Context, in *model.FileDeleteInput) (*model.FileDeleteOutput, error) {
	// 初始化S3客户端
	if err := s.initS3Client(ctx); err != nil {
		return nil, err
	}

	// 检查文件是否存在
	exists, err := s.checkFileExists(ctx, in.FileName)
	if err != nil {
		return &model.FileDeleteOutput{
			Success: false,
			Message: "检查文件失败: " + err.Error(),
		}, nil
	}

	if !exists {
		// 文件不存在，直接返回成功
		return &model.FileDeleteOutput{
			Success: true,
			Message: "文件删除成功",
		}, nil
	}

	// 删除文件
	deleteObjectInput := &s3.DeleteObjectInput{
		Bucket: aws.String(s.config.BucketName),
		Key:    aws.String(in.FileName),
	}

	_, err = s.s3Client.DeleteObject(ctx, deleteObjectInput)
	if err != nil {
		return &model.FileDeleteOutput{
			Success: false,
			Message: "删除文件失败: " + err.Error(),
		}, nil
	}

	return &model.FileDeleteOutput{
		Success: true,
		Message: "文件删除成功",
	}, nil
}

// checkFileExists 检查文件是否存在
func (s *sFile) checkFileExists(ctx context.Context, fileName string) (bool, error) {
	headObjectInput := &s3.HeadObjectInput{
		Bucket: aws.String(s.config.BucketName),
		Key:    aws.String(fileName),
	}

	_, err := s.s3Client.HeadObject(ctx, headObjectInput)
	if err != nil {
		var noSuchKey *types.NoSuchKey
		if gerror.Is(err, noSuchKey) {
			return false, nil
		}
		return false, gerror.NewCode(CodeFileStorageError, "检查文件失败: "+err.Error())
	}

	return true, nil
}

// generateFileName 生成唯一的文件名
func (s *sFile) generateFileName(userId int64, fileType, contentType string) string {
	// 获取文件扩展名
	extension := s.getExtensionFromContentType(contentType)

	// 生成唯一ID
	uniqueId := gstr.Replace(guid.S(), "-", "")

	// 生成时间戳
	timestamp := gtime.Now().Unix()

	// 构建文件名：fileType/userId/timestamp_uniqueId.extension
	return fmt.Sprintf("%s/%d/%d_%s.%s", fileType, userId, timestamp, uniqueId, extension)
}

// getExtensionFromContentType 从ContentType获取文件扩展名
func (s *sFile) getExtensionFromContentType(contentType string) string {
	switch contentType {
	case "image/jpeg":
		return "jpg"
	case "image/jpg":
		return "jpg"
	case "image/png":
		return "png"
	case "image/gif":
		return "gif"
	case "image/webp":
		return "webp"
	case "application/pdf":
		return "pdf"
	case "text/plain":
		return "txt"
	case "application/json":
		return "json"
	default:
		// 尝试从contentType中提取
		parts := strings.Split(contentType, "/")
		if len(parts) == 2 {
			return parts[1]
		}
		return "bin"
	}
}

// validateFileType 验证文件类型
func (s *sFile) validateFileType(ctx context.Context, contentType string) error {
	allowedTypes := g.Cfg().MustGet(ctx, "file.validation.allowedTypes").String()
	if allowedTypes == "" {
		return nil // 如果没有配置限制，则允许所有类型
	}

	extension := s.getExtensionFromContentType(contentType)
	allowedList := strings.Split(allowedTypes, ",")

	for _, allowed := range allowedList {
		if strings.TrimSpace(strings.ToLower(allowed)) == strings.ToLower(extension) {
			return nil
		}
	}

	return gerror.NewCode(CodeFileTypeNotAllowed, fmt.Sprintf("不支持的文件类型: %s，允许的类型: %s", extension, allowedTypes))
}
