package model

// ==================== 营养分析相关结构体 ====================

// NutritionStat 营养统计数据
type NutritionStat struct {
	Date              string  `json:"date" dc:"日期"`
	Calorie           int     `json:"calorie" dc:"热量(千卡)"`
	Protein           float64 `json:"protein" dc:"蛋白质(g)"`
	Carbs             float64 `json:"carbs" dc:"碳水化合物(g)"`
	Fat               float64 `json:"fat" dc:"脂肪(g)"`
	CaloriePercentage float64 `json:"caloriePercentage" dc:"热量目标达成百分比"`
	ProteinPercentage float64 `json:"proteinPercentage" dc:"蛋白质目标达成百分比"`
	CarbsPercentage   float64 `json:"carbsPercentage" dc:"碳水目标达成百分比"`
	FatPercentage     float64 `json:"fatPercentage" dc:"脂肪目标达成百分比"`
}

// NutritionDetailItem 营养素摄入详情项
type NutritionDetailItem struct {
	Name       string  `json:"name" dc:"营养素名称"`
	Value      float64 `json:"value" dc:"营养素值"`
	Unit       string  `json:"unit" dc:"营养素单位"`
	Percentage float64 `json:"percentage" dc:"完成百分比"`
}

// NutritionAdviceDisplay 营养建议显示
type NutritionAdviceDisplay struct {
	Type        string `json:"type" dc:"建议类型: warning/info/danger/success"`
	Title       string `json:"title" dc:"建议标题"`
	Description string `json:"description" dc:"建议详情"`
}

// NutritionTrend 营养趋势数据
type NutritionTrend struct {
	DateList    []string  `json:"dateList" dc:"日期列表"`
	CalorieList []int     `json:"calorieList" dc:"热量列表"`
	ProteinList []float64 `json:"proteinList" dc:"蛋白质列表"`
	CarbsList   []float64 `json:"carbsList" dc:"碳水列表"`
	FatList     []float64 `json:"fatList" dc:"脂肪列表"`
}

// ==================== 获取用户日营养统计 ====================

// NutritionDailyStatInput 获取用户日营养统计输入
type NutritionDailyStatInput struct {
	UserId int64  `json:"userId" dc:"用户ID"`
	Date   string `json:"date" dc:"日期"`
}

// NutritionDailyStatOutput 获取用户日营养统计输出
type NutritionDailyStatOutput struct {
	Stat NutritionStat `json:"stat" dc:"营养统计数据"`
}

// ==================== 获取营养趋势 ====================

// NutritionTrendInput 获取营养趋势输入
type NutritionTrendInput struct {
	UserId    int64  `json:"userId" dc:"用户ID"`
	StartDate string `json:"startDate" dc:"开始日期"`
	EndDate   string `json:"endDate" dc:"结束日期"`
}

// NutritionTrendOutput 获取营养趋势输出
type NutritionTrendOutput struct {
	Trend NutritionTrend `json:"trend" dc:"营养趋势数据"`
}

// ==================== 获取营养摄入详情 ====================

// NutritionDetailsInput 获取营养摄入详情输入
type NutritionDetailsInput struct {
	UserId int64  `json:"userId" dc:"用户ID"`
	Date   string `json:"date" dc:"日期"`
}

// NutritionDetailsOutput 获取营养摄入详情输出
type NutritionDetailsOutput struct {
	Details []NutritionDetailItem `json:"details" dc:"营养摄入详情列表"`
}

// ==================== 获取营养建议 ====================

// NutritionAdviceInput 获取营养建议输入
type NutritionAdviceInput struct {
	UserId int64  `json:"userId" dc:"用户ID"`
	Date   string `json:"date" dc:"日期"`
}

// NutritionAdviceOutput 获取营养建议输出
type NutritionAdviceOutput struct {
	Advices []NutritionAdviceDisplay `json:"advices" dc:"营养建议列表"`
}

// ==================== 计算营养达标率 ====================

// NutritionComplianceRateInput 计算营养达标率输入
type NutritionComplianceRateInput struct {
	Date string `json:"date" dc:"日期"`
}

// NutritionComplianceRateOutput 计算营养达标率输出
type NutritionComplianceRateOutput struct {
	ComplianceRate float64 `json:"complianceRate" dc:"营养达标率(0-100)"`
}

// ==================== 获取所有用户营养趋势(供仪表盘使用) ====================

// NutritionAllTrendInput 获取所有用户营养趋势输入
type NutritionAllTrendInput struct {
	Period string `json:"period" dc:"时间周期: week/month/year"`
}

// NutritionAllTrendOutput 获取所有用户营养趋势输出
type NutritionAllTrendOutput struct {
	DateList    []string  `json:"dateList" dc:"日期列表"`
	CalorieList []float64 `json:"calorieList" dc:"平均热量列表"`
	ProteinList []float64 `json:"proteinList" dc:"平均蛋白质列表"`
	CarbsList   []float64 `json:"carbsList" dc:"平均碳水列表"`
	FatList     []float64 `json:"fatList" dc:"平均脂肪列表"`
}
