package model

// ==================== 仪表盘相关结构体 ====================

// DashboardStats 仪表盘统计数据
type DashboardStats struct {
	TotalUsers               int64   `json:"totalUsers" dc:"总用户数"`
	TodayRecords            int     `json:"todayRecords" dc:"今日饮食记录数"`
	NutritionComplianceRate float64 `json:"nutritionComplianceRate" dc:"营养达标率（百分比）"`
	RecommendationAccuracy  int     `json:"recommendationAccuracy" dc:"推荐准确率（百分比）"`
	StatisticsDate          string  `json:"statisticsDate" dc:"统计日期"`
	ActiveUsers             int64   `json:"activeUsers" dc:"活跃用户数"`
	WeeklyNewUsers          int64   `json:"weeklyNewUsers" dc:"本周新增用户数"`
	MonthlyNewUsers         int64   `json:"monthlyNewUsers" dc:"本月新增用户数"`
}

// DashboardNutritionTrend 仪表盘营养趋势数据
type DashboardNutritionTrend struct {
	DateList    []string  `json:"dateList" dc:"日期列表"`
	CalorieList []float64 `json:"calorieList" dc:"热量列表"`
	ProteinList []float64 `json:"proteinList" dc:"蛋白质列表"`
	CarbsList   []float64 `json:"carbsList" dc:"碳水列表"`
	FatList     []float64 `json:"fatList" dc:"脂肪列表"`
	Period      string    `json:"period" dc:"时间周期"`
	DataPoints  int       `json:"dataPoints" dc:"数据点数量"`
}

// PopularFood 热门食物
type PopularFood struct {
	FoodId       int64  `json:"foodId" dc:"食物ID"`
	FoodName     string `json:"foodName" dc:"食物名称"`
	ImageUrl     string `json:"imageUrl" dc:"食物图片URL"`
	UsageCount   int64  `json:"usageCount" dc:"使用次数"`
	UserCount    int64  `json:"userCount" dc:"使用用户数"`
	Rank         int    `json:"rank" dc:"排名"`
	Calorie      int    `json:"calorie" dc:"热量（每100g）"`
	CategoryName string `json:"categoryName" dc:"分类名称"`
}

// ==================== 获取仪表盘统计数据 ====================

// DashboardStatsInput 获取仪表盘统计数据输入
type DashboardStatsInput struct {
	Date string `json:"date" dc:"统计日期(可选，默认今天)"`
}

// DashboardStatsOutput 获取仪表盘统计数据输出
type DashboardStatsOutput struct {
	Stats DashboardStats `json:"stats" dc:"统计数据"`
}

// ==================== 获取营养摄入趋势 ====================

// DashboardNutritionTrendInput 获取营养摄入趋势输入
type DashboardNutritionTrendInput struct {
	Period string `json:"period" dc:"时间周期: week/month/year"`
}

// DashboardNutritionTrendOutput 获取营养摄入趋势输出
type DashboardNutritionTrendOutput struct {
	Trend DashboardNutritionTrend `json:"trend" dc:"营养趋势数据"`
}

// ==================== 获取最新饮食记录列表 ====================

// DashboardLatestDietRecordsInput 获取最新饮食记录列表输入
type DashboardLatestDietRecordsInput struct {
	UserId    *int64 `json:"userId" dc:"用户ID(可选)"`
	StartDate string `json:"startDate" dc:"开始日期(可选)"`
	EndDate   string `json:"endDate" dc:"结束日期(可选)"`
	MealType  string `json:"mealType" dc:"餐次类型(可选)"`
	Page      int    `json:"page" dc:"当前页"`
	Size      int    `json:"size" dc:"每页大小"`
}

// DashboardLatestDietRecordsOutput 获取最新饮食记录列表输出
type DashboardLatestDietRecordsOutput struct {
	List  []DietRecordItem `json:"list" dc:"记录列表"`
	Page  int              `json:"page" dc:"当前页"`
	Size  int              `json:"size" dc:"每页大小"`
	Total int              `json:"total" dc:"总数"`
}

// ==================== 获取饮食记录详情 ====================

// DashboardDietRecordDetailInput 获取饮食记录详情输入
type DashboardDietRecordDetailInput struct {
	RecordId int64 `json:"recordId" dc:"记录ID"`
}

// DashboardDietRecordDetailOutput 获取饮食记录详情输出
type DashboardDietRecordDetailOutput struct {
	Record DietRecordItem `json:"record" dc:"饮食记录详情"`
}

// ==================== 获取热门食物统计 ====================

// DashboardPopularFoodsInput 获取热门食物统计输入
type DashboardPopularFoodsInput struct {
	Period string `json:"period" dc:"时间周期: week/month/quarter"`
	Limit  int    `json:"limit" dc:"返回数量限制"`
}

// DashboardPopularFoodsOutput 获取热门食物统计输出
type DashboardPopularFoodsOutput struct {
	Foods []PopularFood `json:"foods" dc:"热门食物列表"`
}

// ==================== 获取用户统计数据 ====================

// DashboardUserStatsInput 获取用户统计数据输入
type DashboardUserStatsInput struct {
	Period string `json:"period" dc:"时间周期: week/month/year"`
}

// DashboardUserStatsOutput 获取用户统计数据输出
type DashboardUserStatsOutput struct {
	TotalUsers      int64 `json:"totalUsers" dc:"总用户数"`
	ActiveUsers     int64 `json:"activeUsers" dc:"活跃用户数"`
	NewUsers        int64 `json:"newUsers" dc:"新增用户数"`
	RetentionRate   float64 `json:"retentionRate" dc:"用户留存率"`
	EngagementRate  float64 `json:"engagementRate" dc:"用户参与率"`
}

// ==================== 获取系统健康状态 ====================

// DashboardSystemHealthInput 获取系统健康状态输入
type DashboardSystemHealthInput struct {
	// 暂时不需要参数
}

// DashboardSystemHealthOutput 获取系统健康状态输出
type DashboardSystemHealthOutput struct {
	Status         string  `json:"status" dc:"系统状态: healthy/warning/error"`
	Uptime         int64   `json:"uptime" dc:"运行时间(秒)"`
	CpuUsage       float64 `json:"cpuUsage" dc:"CPU使用率"`
	MemoryUsage    float64 `json:"memoryUsage" dc:"内存使用率"`
	DiskUsage      float64 `json:"diskUsage" dc:"磁盘使用率"`
	DatabaseStatus string  `json:"databaseStatus" dc:"数据库状态"`
	CacheStatus    string  `json:"cacheStatus" dc:"缓存状态"`
}
