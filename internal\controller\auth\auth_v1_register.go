package auth

import (
	"context"

	"shikeyinxiang-goframe/api/auth/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) Register(ctx context.Context, req *v1.RegisterReq) (res *v1.RegisterRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.RegisterInput{
		Email:    req.Email,
		Password: req.Password,
		Username: req.Username,
	}

	// 2. 调用业务逻辑
	output, err := service.Auth().Register(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.RegisterRes{
		Success: output.Success,
		Message: output.Message,
		UserId:  output.UserId,
	}

	return res, nil
}
