package diet

import (
	"context"

	"shikeyinxiang-goframe/api/diet/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) DietRecordList(ctx context.Context, req *v1.DietRecordListReq) (res *v1.DietRecordListRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.DietRecordListInput{
		UserId:    req.UserId,
		Page:      req.Page,
		PageSize:  req.PageSize,
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
		MealType:  req.MealType,
	}

	// 2. 调用业务逻辑
	output, err := service.Diet().GetUserDietRecords(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.DietRecordListRes{
		List:     output.List,
		Total:    output.Total,
		Page:     output.Page,
		PageSize: output.PageSize,
	}

	return res, nil
}
