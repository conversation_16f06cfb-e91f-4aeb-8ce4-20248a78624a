package dashboard

import (
	"context"

	"shikeyinxiang-goframe/api/dashboard/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) UserStats(ctx context.Context, req *v1.UserStatsReq) (res *v1.UserStatsRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.UserStatsInput{
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
	}

	// 2. 调用业务逻辑
	output, err := service.Dashboard().GetUserStats(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.UserStatsRes{
		NewUsers:    output.NewUsers,
		ActiveUsers: output.ActiveUsers,
		TotalUsers:  output.TotalUsers,
		UserGrowth:  output.UserGrowth,
	}

	return res, nil
}
