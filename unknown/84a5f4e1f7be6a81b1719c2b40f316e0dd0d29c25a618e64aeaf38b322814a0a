package dashboard

import (
	"context"

	"shikeyinxiang-goframe/api/dashboard/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) DietRecordDetail(ctx context.Context, req *v1.DietRecordDetailReq) (res *v1.DietRecordDetailRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.DietRecordDetailInput{
		RecordId: req.RecordId,
	}

	// 2. 调用业务逻辑
	output, err := service.Dashboard().GetDietRecordDetail(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.DietRecordDetailRes{
		RecordInfo: output.RecordInfo,
	}

	return res, nil
}
