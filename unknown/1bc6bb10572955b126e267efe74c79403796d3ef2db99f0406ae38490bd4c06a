package diet

import (
	"context"

	"shikeyinxiang-goframe/api/diet/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) DietRecordDelete(ctx context.Context, req *v1.DietRecordDeleteReq) (res *v1.DietRecordDeleteRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.DietRecordDeleteInput{
		RecordId: req.RecordId,
	}

	// 2. 调用业务逻辑
	output, err := service.Diet().DeleteDietRecord(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.DietRecordDeleteRes{
		Success: output.Success,
		Message: output.Message,
	}

	return res, nil
}
