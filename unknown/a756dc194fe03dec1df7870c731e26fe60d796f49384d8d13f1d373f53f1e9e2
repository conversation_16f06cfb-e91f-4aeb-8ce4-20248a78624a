package diet

import (
	"context"

	"shikeyinxiang-goframe/api/diet/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) DietRecordStats(ctx context.Context, req *v1.DietRecordStatsReq) (res *v1.DietRecordStatsRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.DietRecordStatsInput{
		Date: req.Date,
	}

	// 2. 调用业务逻辑
	output, err := service.Diet().GetDietRecordStats(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.DietRecordStatsRes{
		TotalRecords: output.TotalRecords,
		Date:         output.Date,
	}

	return res, nil
}
