package dashboard

import (
	"context"

	"shikeyinxiang-goframe/api/dashboard/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) SystemHealth(ctx context.Context, req *v1.SystemHealthReq) (res *v1.SystemHealthRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.SystemHealthInput{}

	// 2. 调用业务逻辑
	output, err := service.Dashboard().GetSystemHealth(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.SystemHealthRes{
		Status:      output.Status,
		Uptime:      output.Uptime,
		MemoryUsage: output.MemoryUsage,
		CpuUsage:    output.CpuUsage,
		DiskUsage:   output.DiskUsage,
		Services:    output.Services,
	}

	return res, nil
}
