package food

import (
	"context"

	"shikeyinxiang-goframe/api/food/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) FoodDetail(ctx context.Context, req *v1.FoodDetailReq) (res *v1.FoodDetailRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.FoodDetailInput{
		FoodId: req.FoodId,
	}

	// 2. 调用业务逻辑
	output, err := service.Food().GetFoodById(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.FoodDetailRes{
		FoodInfo: output.FoodInfo,
	}

	return res, nil
}
