package auth

import (
	"context"

	"shikeyinxiang-goframe/api/auth/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) AdminLogin(ctx context.Context, req *v1.AdminLoginReq) (res *v1.AdminLoginRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.AdminLoginInput{
		Username: req.Username,
		Password: req.Password,
	}

	// 2. 调用业务逻辑
	output, err := service.Auth().AdminLogin(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.AdminLoginRes{
		Token:    output.Token,
		UserInfo: output.UserInfo,
	}

	return res, nil
}
