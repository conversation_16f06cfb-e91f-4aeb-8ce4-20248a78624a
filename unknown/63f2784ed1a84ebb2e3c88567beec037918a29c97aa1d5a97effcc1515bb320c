package food

import (
	"context"

	"shikeyinxiang-goframe/api/food/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) FoodUpdate(ctx context.Context, req *v1.FoodUpdateReq) (res *v1.FoodUpdateRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.FoodUpdateInput{
		FoodId:      req.FoodId,
		Name:        req.Name,
		CategoryId:  req.CategoryId,
		Calories:    req.Calories,
		Protein:     req.Protein,
		Fat:         req.Fat,
		Carbs:       req.Carbs,
		Fiber:       req.Fiber,
		Sugar:       req.Sugar,
		Sodium:      req.Sodium,
		Description: req.Description,
		ImageUrl:    req.ImageUrl,
		Unit:        req.Unit,
		UnitWeight:  req.UnitWeight,
	}

	// 2. 调用业务逻辑
	output, err := service.Food().UpdateFood(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.FoodUpdateRes{
		Success: output.Success,
		Message: output.Message,
	}

	return res, nil
}
