package dashboard

import "github.com/gogf/gf/v2/errors/gcode"

// 仪表盘模块错误码定义
// 错误码格式：AABBBCCC (AA=70仪表盘模块, BBB=001-999功能, CCC=001-999具体错误)

// ==================== 仪表盘基础错误 (70001xxx) ====================

var (
	// CodeDashboardDataNotFound 仪表盘数据不存在
	CodeDashboardDataNotFound = gcode.New(70001001, "仪表盘数据不存在", nil)
	
	// CodeDashboardStatsCalculationFailed 统计数据计算失败
	CodeDashboardStatsCalculationFailed = gcode.New(70001002, "统计数据计算失败", nil)
	
	// CodeDashboardInvalidDateRange 日期范围无效
	CodeDashboardInvalidDateRange = gcode.New(70001003, "日期范围无效", nil)
)

// ==================== 趋势数据相关错误 (70002xxx) ====================

var (
	// CodeDashboardTrendDataInsufficient 趋势数据不足
	CodeDashboardTrendDataInsufficient = gcode.New(70002001, "趋势数据不足", nil)
	
	// CodeDashboardTrendCalculationFailed 趋势计算失败
	CodeDashboardTrendCalculationFailed = gcode.New(70002002, "趋势计算失败", nil)
	
	// CodeDashboardTrendPeriodInvalid 趋势周期无效
	CodeDashboardTrendPeriodInvalid = gcode.New(70002003, "趋势周期无效", nil)
)

// ==================== 报告生成相关错误 (70003xxx) ====================

var (
	// CodeDashboardReportGenerationFailed 报告生成失败
	CodeDashboardReportGenerationFailed = gcode.New(70003001, "报告生成失败", nil)
	
	// CodeDashboardReportDataIncomplete 报告数据不完整
	CodeDashboardReportDataIncomplete = gcode.New(70003002, "报告数据不完整", nil)
	
	// CodeDashboardReportFormatError 报告格式错误
	CodeDashboardReportFormatError = gcode.New(70003003, "报告格式错误", nil)
)

// ==================== 权限相关错误 (70004xxx) ====================

var (
	// CodeDashboardPermissionDenied 无权限访问仪表盘
	CodeDashboardPermissionDenied = gcode.New(70004001, "无权限访问仪表盘", nil)
	
	// CodeDashboardAdminRequired 需要管理员权限
	CodeDashboardAdminRequired = gcode.New(70004002, "需要管理员权限", nil)
	
	// CodeDashboardUserAccessDenied 用户访问被拒绝
	CodeDashboardUserAccessDenied = gcode.New(70004003, "用户访问被拒绝", nil)
)

// ==================== 服务相关错误 (70005xxx) ====================

var (
	// CodeDashboardServiceTimeout 仪表盘服务超时
	CodeDashboardServiceTimeout = gcode.New(70005001, "仪表盘服务超时", nil)
	
	// CodeDashboardServiceUnavailable 仪表盘服务不可用
	CodeDashboardServiceUnavailable = gcode.New(70005002, "仪表盘服务不可用", nil)
	
	// CodeDashboardServiceOverload 仪表盘服务过载
	CodeDashboardServiceOverload = gcode.New(70005003, "仪表盘服务过载", nil)
)

// ==================== 缓存相关错误 (70006xxx) ====================

var (
	// CodeDashboardCacheError 仪表盘缓存错误
	CodeDashboardCacheError = gcode.New(70006001, "仪表盘缓存错误", nil)
	
	// CodeDashboardCacheExpired 仪表盘缓存过期
	CodeDashboardCacheExpired = gcode.New(70006002, "仪表盘缓存过期", nil)
	
	// CodeDashboardCacheCorrupted 仪表盘缓存损坏
	CodeDashboardCacheCorrupted = gcode.New(70006003, "仪表盘缓存损坏", nil)
)

// ==================== 数据一致性相关错误 (70007xxx) ====================

var (
	// CodeDashboardDataInconsistent 数据不一致
	CodeDashboardDataInconsistent = gcode.New(70007001, "数据不一致", nil)
	
	// CodeDashboardDataSyncFailed 数据同步失败
	CodeDashboardDataSyncFailed = gcode.New(70007002, "数据同步失败", nil)
	
	// CodeDashboardDataValidationFailed 数据验证失败
	CodeDashboardDataValidationFailed = gcode.New(70007003, "数据验证失败", nil)
)

// ==================== 导出相关错误 (70008xxx) ====================

var (
	// CodeDashboardExportFailed 数据导出失败
	CodeDashboardExportFailed = gcode.New(70008001, "数据导出失败", nil)
	
	// CodeDashboardExportFormatUnsupported 不支持的导出格式
	CodeDashboardExportFormatUnsupported = gcode.New(70008002, "不支持的导出格式", nil)
	
	// CodeDashboardExportSizeExceeded 导出数据量超限
	CodeDashboardExportSizeExceeded = gcode.New(70008003, "导出数据量超限", nil)
)
