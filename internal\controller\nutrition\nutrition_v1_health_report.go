package nutrition

import (
	"context"

	"shikeyinxiang-goframe/api/nutrition/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) HealthReport(ctx context.Context, req *v1.HealthReportReq) (res *v1.HealthReportRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.HealthReportInput{
		UserId:    req.UserId,
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
	}

	// 2. 调用业务逻辑
	output, err := service.Nutrition().GenerateHealthReport(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.HealthReportRes{
		Report:      output.Report,
		Summary:     output.Summary,
		Suggestions: output.Suggestions,
	}

	return res, nil
}
