package nutrition

import (
	"context"

	"shikeyinxiang-goframe/api/nutrition/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) NutritionTrend(ctx context.Context, req *v1.NutritionTrendReq) (res *v1.NutritionTrendRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.NutritionTrendInput{
		UserId:    req.UserId,
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
		Period:    req.Period,
	}

	// 2. 调用业务逻辑
	output, err := service.Nutrition().GetNutritionTrend(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.NutritionTrendRes{
		TrendData: output.TrendData,
		Period:    output.Period,
	}

	return res, nil
}
