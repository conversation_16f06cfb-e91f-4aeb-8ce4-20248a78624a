package food

import (
	"context"

	"shikeyinxiang-goframe/api/food/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) CategoryDelete(ctx context.Context, req *v1.CategoryDeleteReq) (res *v1.CategoryDeleteRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.CategoryDeleteInput{
		CategoryId: req.CategoryId,
	}

	// 2. 调用业务逻辑
	output, err := service.FoodCategory().DeleteCategory(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.CategoryDeleteRes{
		Success: output.Success,
		Message: output.Message,
	}

	return res, nil
}
