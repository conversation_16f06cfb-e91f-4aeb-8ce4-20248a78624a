package auth

import (
	"context"

	"shikeyinxiang-goframe/api/auth/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) UserLogin(ctx context.Context, req *v1.UserLoginReq) (res *v1.UserLoginRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.UserLoginInput{
		Email:    req.Email,
		Password: req.Password,
	}

	// 2. 调用业务逻辑
	output, err := service.Auth().UserLogin(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.UserLoginRes{
		Token:    output.Token,
		UserInfo: output.UserInfo,
	}

	return res, nil
}
