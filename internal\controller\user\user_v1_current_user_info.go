package user

import (
	"context"

	"shikeyinxiang-goframe/api/user/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) CurrentUserInfo(ctx context.Context, req *v1.CurrentUserInfoReq) (res *v1.CurrentUserInfoRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.CurrentUserInfoInput{
		UserId: req.UserId,
	}

	// 2. 调用业务逻辑
	output, err := service.User().GetCurrentUserInfo(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.CurrentUserInfoRes{
		UserInfo: output.UserInfo,
	}

	return res, nil
}
