package main

import (
	_ "shikeyinxiang-goframe/internal/packed"

	"github.com/gogf/gf/v2/os/gctx"

	"shikeyinxiang-goframe/internal/cmd"
	_ "shikeyinxiang-goframe/internal/logic/auth"      // 确保auth logic包被导入，执行init()
	_ "shikeyinxiang-goframe/internal/logic/food"      // 确保food logic包被导入，执行init()
	_ "shikeyinxiang-goframe/internal/logic/user"      // 确保user logic包被导入，执行init()
	_ "shikeyinxiang-goframe/internal/logic/file"      // 确保file logic包被导入，执行init()
	_ "shikeyinxiang-goframe/internal/logic/diet"      // 确保diet logic包被导入，执行init()
	_ "shikeyinxiang-goframe/internal/logic/nutrition" // 确保nutrition logic包被导入，执行init()
	_ "shikeyinxiang-goframe/internal/logic/dashboard" // 确保dashboard logic包被导入，执行init()
)

func main() {
	cmd.Main.Run(gctx.GetInitCtx())
}
