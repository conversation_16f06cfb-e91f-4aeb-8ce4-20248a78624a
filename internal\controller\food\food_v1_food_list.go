package food

import (
	"context"

	"shikeyinxiang-goframe/api/food/v1"
	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

func (c *ControllerV1) FoodList(ctx context.Context, req *v1.FoodListReq) (res *v1.FoodListRes, err error) {
	// 1. 参数转换：API请求参数 → 业务层Input
	input := &model.FoodListInput{
		Page:       req.Page,
		PageSize:   req.PageSize,
		Name:       req.Name,
		CategoryId: req.CategoryId,
	}

	// 2. 调用业务逻辑
	output, err := service.Food().GetFoodList(ctx, input)
	if err != nil {
		// 错误直接透传，由中间件处理
		return nil, err
	}

	// 3. 参数转换：业务层Output → API响应
	res = &v1.FoodListRes{
		List:     output.List,
		Total:    output.Total,
		Page:     output.Page,
		PageSize: output.PageSize,
	}

	return res, nil
}
